/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: SplashActivity.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.main.ui;

import android.content.DialogInterface;
import android.os.Build;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import com.bumptech.glide.Glide;

import java.io.File;
import java.math.BigDecimal;

import io.bhex.app.BuildConfig;
import io.bhex.app.R;
import io.bhex.app.app.BHexApplication;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.main.presenter.SplashPresenter;
import io.bhex.app.safe.SafeUilts;
import io.bhex.app.utils.BasicFunctionsUtil;
import io.bhex.app.utils.TextColorUtils;
import io.bhex.app.view.CustomListAlertDialog;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.core.SPEx;
import io.bhex.sdk.UrlsConfig;
import io.bhex.sdk.account.UserManager;
import io.bhex.sdk.app.AppStatusConstant;
import io.bhex.sdk.app.AppStatusManager;
import io.bhex.sdk.data_manager.MMKVManager;
import io.bhex.sdk.data_manager.RateAndLocalManager;

import static io.bhex.sdk.UrlsConfig.API_SERVER_URL;

/**
 * ================================================
 * 描   述：App 启动页面
 * ================================================
 */

public class SplashActivity extends BaseActivity<SplashPresenter, SplashPresenter.SplashUI> implements SplashPresenter.SplashUI, View.OnClickListener {
    private static final String DEFAULT_SHOW_DURATION_TIME = "1.5";   //默认显示时长
    private ImageView launchBgView;
    private TextView btnSkip;
    private CountDownTimer showCountDownTimer;

    @Override
    protected int getContentView() {
        return R.layout.activity_splash_layout;
    }

    @Override
    protected SplashPresenter createPresenter() {
        return new SplashPresenter();
    }

    @Override
    protected SplashPresenter.SplashUI getUI() {
        return this;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
//          requestWindowFeature(Window.FEATURE_NO_TITLE);
//          getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,WindowManager.LayoutParams.FLAG_FULLSCREEN);
        super.onCreate(savedInstanceState);
        AppData.HOME_LOCKED = true;
        UserManager.getInstance().updateFingerAuthStatus(false);

        if (SafeUilts.isOpenSageVerify()) {//开启了指纹或者手势
            AppData.isFirstLaunch = true;
        }
        //StatusBarExtUtil.setStatusColor(this,false,true,R.color.blue2);


        //StatusBarExtUtil.setStatusColor(this,false,true,R.color.white);

    }

    @Override
    protected void onResume() {
        super.onResume();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            //实现状态栏图标和文字颜色为浅色
            getWindow().getDecorView().setSystemUiVisibility(
                    View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
            getWindow().getDecorView().findViewById(android.R.id.content).setPadding(0, 0, 0, 0);
        }
    }


    @Override
    protected void initView() {
        super.initView();
        // 已删除环境切换功能，简化配置通过修改gradle.properties中的DOMAIN实现
        launchBgView = viewFinder.imageView(R.id.launch_bg);
        loadLaunchBg();

        btnSkip = viewFinder.textView(R.id.btn_skip);

        startShowCountDownTimer();
    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.textView(R.id.btn_skip).setOnClickListener(this);
    }

    private void loadLaunchBg() {
//        https://static.bhfastime.com/bhop/image/NA6eIkv7Ohc-4ucUpqY9V-UTbSBXlnEStZhFL_83A18.png
        String launchBgFilePath = MMKVManager.getInstance().mmkv().decodeString("launch_bg_file_path_"+ RateAndLocalManager.GetInstance(BHexApplication.getInstance()).getCurLocalKind().code);

//        String launchBgUrl = "https://static.bhfastime.com/bhop/image/NA6eIkv7Ohc-4ucUpqY9V-UTbSBXlnEStZhFL_83A18.png";
//        if (!TextUtils.isEmpty(launchBgUrl)) {
//
//            Glide.with(this)
//                    .load(launchBgUrl)
//                    .fitCenter()
//                    .placeholder(R.mipmap.launch_bg)
//                    .error(R.mipmap.launch_bg)
//                    .fallback(R.mipmap.launch_bg)
//                    .diskCacheStrategy(DiskCacheStrategy.SOURCE)
//                    .into(launchBgView);
//        }
        boolean supportNewSplash = BasicFunctionsUtil.getBasicFunctionsConfig().isSupportNewSplash();
        if (!TextUtils.isEmpty(launchBgFilePath)) {
            File launchImgFile = new File(launchBgFilePath);
            if (launchImgFile.exists()) {
                //有缓存启动图文件
                Glide.with(this)
                        .load(launchImgFile)
                        .fitCenter()
                        .error(R.mipmap.launch_bg)
                        .fallback(R.mipmap.launch_bg)
                        .into(launchBgView);

                return;
            }
        }
        //没有缓存启动页,设置默认图
        if (supportNewSplash) {//配置了新启动图，那就居中适配fitCenter
            launchBgView.setScaleType(ImageView.ScaleType.FIT_CENTER);
        }else{//没有配置，就大图裁边
            launchBgView.setScaleType(ImageView.ScaleType.CENTER_CROP);
        }
        launchBgView.setImageResource(R.mipmap.launch_bg);
    }

    @Override
    protected boolean translucentStatusBar() {
        return true;
    }

    @Override
    public boolean onKeyUp(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            getPresenter().exit();
            return true;
        }
        return super.onKeyUp(keyCode, event);
    }



    @Override
    protected void onStop() {
        super.onStop();
        //APPConfig.getInstance().initUmengShare();
        //APPConfig.getInstance().initDeepKnow();
    }

    @Override
    protected boolean isStatusColorDefault() {
        return false;
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.btn_skip:
                if (showCountDownTimer != null) {
                    showCountDownTimer.cancel();
                }
                SplashActivity.this.getPresenter().startApp();
                break;
        }
    }

    private void startShowCountDownTimer() {
        String lastSeconds = MMKVManager.getInstance().mmkv().decodeString("ads_show_time",DEFAULT_SHOW_DURATION_TIME);
        Long remainingTime = new BigDecimal(lastSeconds).multiply(new BigDecimal("1000")).longValue();
//        Long remainingTime = Long.valueOf(lastSeconds) * 1000;
        if (remainingTime < 2000) {    //小于2s就不做倒计时提示了
            btnSkip.setVisibility(View.GONE);
        }else{
            btnSkip.setVisibility(View.VISIBLE);
        }
        if (remainingTime > 0) {
            if (showCountDownTimer != null) {
                return;
            }
            showCountDownTimer = new CountDownTimer(remainingTime, 1000) {

                @Override
                public void onTick(long millisUntilFinished) {
                    int s = (int) (millisUntilFinished / 1000);
                    String remainingTime = s + "s";
                    TextColorUtils.setTextViewColor(btnSkip, "%s " + getString(R.string.string_skip), remainingTime, R.color.blue);
                }

                @Override
                public void onFinish() {
//                    btnSkip.setText(getString(R.string.string_skip));
                    SplashActivity.this.getPresenter().startApp();

                }

            }.start();

        } else {
            SplashActivity.this.getPresenter().startApp();
        }
    }
}
