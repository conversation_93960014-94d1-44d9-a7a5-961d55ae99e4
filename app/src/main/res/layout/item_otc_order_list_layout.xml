<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: item_otc_order_list_layout.xml
  ~   @Date: 19-1-25 上午11:17
  ~   @Author: ppzhao
  ~   @Description:
  ~   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/itemView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/item_style"
    android:paddingBottom="@dimen/dip_8"
    android:orientation="vertical"
    >

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/app_margin_top"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:layout_marginRight="@dimen/app_margin_right">

        <TextView
            android:id="@+id/order_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Dark_Bold" />
        <TextView
            android:id="@+id/order_quantity"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toRightOf="@id/order_type"
            android:layout_marginLeft="@dimen/dip_4"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Dark_Bold" />

        <TextView
            android:id="@+id/order_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_2"
            android:layout_below="@id/order_type"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Caption_Grey" />

        <ImageView
            android:id="@+id/order_status_img"
            android:layout_width="@dimen/dip_20"
            android:layout_height="@dimen/dip_20"
            android:src="@mipmap/icon_otc_finished"
            android:gravity="center_vertical"
            android:layout_alignParentRight="true"
            android:maxLines="1"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyS_Grey" />

        <TextView
            android:id="@+id/order_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:layout_toLeftOf="@id/order_status_img"
            android:layout_marginRight="@dimen/dip_4"
            android:layout_marginTop="@dimen/dip_2"
            android:maxLines="1"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyS_Grey" />


    </RelativeLayout>

    <io.bhex.app.skin.view.SkinPercentRelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:layout_marginRight="@dimen/app_margin_right"
        android:layout_marginBottom="@dimen/dip_16"
        android:layout_marginTop="@dimen/dip_8">

        <LinearLayout
            android:id="@+id/linear1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:orientation="vertical"
            app:layout_widthPercent="40%">

            <TextView
                android:id="@+id/unitPriceTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:text="@string/string_unit"
                android:textAppearance="@style/Body_Grey" />

            <TextView
                android:id="@+id/totalAmountTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dip_4"
                android:gravity="center_vertical"
                android:text="@string/string_total_price"
                android:textAppearance="@style/Body_Grey" />

            <TextView
                android:id="@+id/targetTraderTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:layout_marginTop="@dimen/dip_4"
                android:text="@string/string_transaction_object"
                android:textAppearance="@style/Body_Grey" />


        </LinearLayout>

        <LinearLayout
            android:id="@+id/linear2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toRightOf="@+id/linear1"
            android:orientation="vertical"
            app:layout_widthPercent="50%">


            <TextView
                android:id="@+id/unitPrice"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/Body_Dark" />

            <TextView
                android:id="@+id/totalAmount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:layout_marginTop="@dimen/dip_4"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/Body_Dark" />


            <TextView
                android:id="@+id/targetTrader"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:layout_marginTop="@dimen/dip_4"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/Body_Dark" />


        </LinearLayout>


    </io.bhex.app.skin.view.SkinPercentRelativeLayout>
    <TextView
        android:id="@+id/order_result"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="@dimen/app_margin_right"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:visibility="visible"
        android:gravity="center_vertical"
        android:layout_marginBottom="@dimen/dip_16"
        android:ellipsize="end"
        android:maxLines="3"
        android:text="@string/string_placeholder"
        android:textAppearance="@style/Body_Dark_Bold" />
    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_8"
        android:background="@color/divider_line_color" />
</LinearLayout>