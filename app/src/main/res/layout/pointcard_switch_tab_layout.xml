<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: include_pointcard_flow_tab_layout.xml
  ~   @Date: 18-12-14 下午3:36
  ~   @Author: ppzhao
  ~   @Description:
  ~   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  -->

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/tabLinear"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/dip_3"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tab_a"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dip_32"
            android:background="@drawable/bg_corner_rect_blue"
            android:paddingStart="@dimen/dip_12"
            android:paddingEnd="@dimen/dip_12"
            android:gravity="center_vertical"
            android:text="@string/string_pointcard_detail"
            android:textAppearance="@style/Body_Blue_Bold"
            android:textAlignment="center"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tab_b"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dip_32"
            android:paddingStart="@dimen/dip_12"
            android:paddingEnd="@dimen/dip_12"
            android:text="@string/string_buy_records"
            android:textAppearance="@style/Body_Grey"
            android:gravity="center_vertical"
            android:textAlignment="center"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toEndOf="@+id/tab_a"
            app:layout_constraintTop_toTopOf="parent" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>