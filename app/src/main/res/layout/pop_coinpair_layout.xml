<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/transparent"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/color_dark_50"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/upRela"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="0.3"
            android:background="@color/white"
            android:orientation="vertical"
            >
            <RelativeLayout
                android:id="@+id/titleRela"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <io.bhex.app.view.TopBar
                    android:id="@+id/topBar"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dip_58"
                    android:layout_marginLeft="@dimen/dip_6"
                    android:visibility="visible"
                    app:left_text=""
                    app:left_visiblity="gone"
                    app:right_text=""
                    app:title_text="@string/title_trade" />

                <io.bhex.app.skin.view.SkinTabLayout
                    android:id="@+id/tabCoin"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dip_48"
                    android:layout_below="@id/topBar"
                    android:layout_marginLeft="@dimen/dip_20"
                    android:visibility="visible"
                    app:tabIndicatorColor="@color/blue"
                    app:tabIndicatorHeight="@dimen/dip_2"
                    app:tabIndicatorFullWidth="false"
                    app:tabMinWidth="@dimen/dip_64"
                    app:tabMode="scrollable"
                    app:tabSelectedTextColor="@color/blue"
                    app:tabTextAppearance="@style/BodyL_Dark_Bold"
                    app:tabTextColor="@color/dark" />

                <RelativeLayout
                    android:id="@+id/rela_search"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dip_58"
                    android:layout_below="@+id/tabCoin"
                    android:visibility="gone"
                    >

                    <ImageView
                        android:id="@+id/close_search"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_alignParentRight="true"
                        android:padding="@dimen/dip_8"
                        android:src="@mipmap/icon_close" />

                    <EditText
                        android:id="@+id/edit_search"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dip_40"
                        android:layout_centerVertical="true"
                        android:layout_toLeftOf="@id/close_search"
                        android:layout_marginLeft="@dimen/app_margin_left"
                        android:paddingLeft="@dimen/dip_8"
                        android:maxLines="1"
                        android:background="@drawable/bg_corner_rect_line_gray"
                        android:gravity="center_vertical"
                        android:hint="@string/string_hint_search"
                        android:textAppearance="@style/H4_Grey_No_Bold"
                        android:textColor="@color/dark50"
                        android:textColorHint="@color/dark50" />


                </RelativeLayout>

            </RelativeLayout>
            <View
                android:id="@+id/divider_top"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dip_1"
                android:layout_below="@id/titleRela"
                android:background="@color/dark5" />


            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/clRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:paddingTop="@dimen/dip_16"
                android:paddingBottom="@dimen/dip_16"
                android:clipToPadding="true"
                app:layout_behavior="@string/appbar_scrolling_view_behavior"
                tools:listitem="@layout/item_rise_fall_list_layout"
                />

            <!-- popwindow + viewpager+fragment bug临时方案 以后处理 -->
            <androidx.viewpager.widget.ViewPager
                android:id="@+id/clViewPager"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

            <View
                android:id="@+id/divider"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dip_1"
                android:visibility="gone"
                android:background="@color/dark5" />




        </LinearLayout>
        <RelativeLayout
            android:id="@+id/bottomRela"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="0.7"
            />
    </LinearLayout>

</RelativeLayout>