<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#99232323" >

    <LinearLayout
        android:layout_width="280dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/update_dialog_bg"
        android:orientation="vertical" >

        <!-- Title -->

        <RelativeLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content" >

            <ImageView
                android:id="@+id/umeng_update_wifi_indicator"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="10dp"
                android:visibility="gone"
                android:src="@mipmap/ic_launcher" />

            <TextView
                android:id="@+id/dialog_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_marginBottom="@dimen/dip_15"
                android:layout_marginTop="@dimen/dip_15"
                android:text="@string/string_version_find_new"
                android:textAppearance="?android:attr/textAppearanceLarge"
                android:textColor="#000000"
                android:textSize="18sp" />

            <Button
                android:id="@+id/umeng_update_id_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="10dp"
                android:focusable="true"
                android:visibility="gone" />
        </RelativeLayout>

        <!-- split -->

        <View
            android:layout_width="fill_parent"
            android:layout_height="0.5dip"
            android:background="#e1dede" />
        <!-- Content -->

        <ScrollView
            android:layout_width="fill_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:padding="10dp"
            android:visibility="visible">

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical" >

                <TextView
                    android:id="@+id/update_content"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:layout_marginRight="5dp"
                    android:layout_marginTop="15dp"
                    android:layout_marginBottom="15dp"
                    android:focusable="true"
                    android:textColor="#AAABAF" />
            </LinearLayout>
        </ScrollView>

        <!-- Ignore CheckBox -->


        <!-- OK&Cancel Button -->

        <View
            android:layout_width="fill_parent"
            android:layout_height="0.5dip"
            android:background="#e1dede" />

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content" >
            <Button
                android:id="@+id/update_id_ok"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/btn_right_selector"
                android:focusable="true"
                android:gravity="center"
                android:padding="12dp"
                android:textColor="#000000"
                android:textSize="16sp" />

            <View
                android:layout_width="0.5dip"
                android:layout_height="fill_parent"
                android:layout_gravity="center_horizontal"
                android:background="#e1dede" />

            <Button
                android:id="@+id/update_id_cancel"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/btn_left_selector"
                android:focusable="true"
                android:gravity="center"
                android:padding="12dp"
                android:textColor="#000000"
                android:textSize="16sp" />
        </LinearLayout>
    </LinearLayout>

</RelativeLayout>