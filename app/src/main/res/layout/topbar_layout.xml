<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/titleRela"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dip_58"
    android:background="@color/color_bg_2"
    >

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_57"
        android:paddingLeft="@dimen/dip_4">


        <RelativeLayout
            android:id="@+id/title_bar_left_layout"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentLeft="true"
            android:visibility="visible">

            <ImageView
                android:id="@+id/title_bar_left_img"
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:paddingTop="@dimen/dip_10"
                android:paddingBottom="@dimen/dip_10"
                android:src="@drawable/title_back_btn" />

            <TextView
                android:id="@+id/title_bar_left_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_toRightOf="@+id/title_bar_left_img"
                android:text="Close"
                android:textAppearance="@style/Body_Dark" />
        </RelativeLayout>


        <LinearLayout
            android:id="@+id/title_layout"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginLeft="@dimen/dip_10"
            android:layout_toRightOf="@id/title_bar_left_layout"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/title_bar_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dip_4"
                android:gravity="center_vertical"
                android:visibility="visible" />

            <TextView
                android:id="@+id/title_bar_text"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_24"
                android:gravity="center_vertical"
                android:drawablePadding="@dimen/dip_4"
                android:textAppearance="@style/H4_Dark"
                tools:text="标题" />

            <TextView
                android:id="@+id/titleTag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="@dimen/dip_20"
                android:gravity="center"
                android:layout_marginLeft="@dimen/dip_8"
                android:text=""
                android:textAppearance="@style/Caption_Blue_Bold"
                android:paddingLeft="@dimen/dip_2"
                android:paddingRight="@dimen/dip_2"
                android:paddingTop="@dimen/dip_1"
                android:paddingBottom="@dimen/dip_1"
                android:maxLines="1"
                android:visibility="gone"
                android:background="@drawable/bg_rect_blue"
                />

        </LinearLayout>


        <LinearLayout
            android:id="@+id/title_bar_right_layout"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:gravity="center_vertical"
            android:paddingRight="@dimen/dip_4">

            <TextView
                android:id="@+id/title_bar_right_text"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_24"
                android:gravity="center_vertical"
                android:layout_marginRight="@dimen/app_margin_right"
                android:textAppearance="@style/Body_Dark"
                android:visibility="visible"
                tools:text="右侧文案" />

            <TextView
                android:id="@+id/title_bar_right_text2"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_24"
                android:gravity="center_vertical"
                android:paddingLeft="@dimen/dip_6"
                android:textAppearance="@style/Body_Dark"
                android:visibility="gone"
                android:layout_marginRight="@dimen/app_margin_right"
                tools:text="" />

            <ImageView
                android:id="@+id/title_bar_right_image2"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:gravity="center_vertical"
                android:paddingTop="@dimen/dip_6"
                android:paddingBottom="@dimen/dip_6"
                android:visibility="gone"
                tools:src="@mipmap/icon_search" />

            <ImageView
                android:id="@+id/title_bar_right_image"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:gravity="center_vertical"
                android:paddingTop="@dimen/dip_6"
                android:paddingLeft="@dimen/dip_1"
                android:paddingBottom="@dimen/dip_6"
                android:visibility="visible"
                tools:src="@mipmap/icon_search" />


        </LinearLayout>
        <ImageView
            android:id="@+id/title_red_point_right"
            android:layout_width="@dimen/dip_6"
            android:layout_height="@dimen/dip_6"
            android:visibility="gone"
            android:background="@drawable/bg_round_red"
            android:layout_alignParentRight="true"
            android:layout_marginEnd="@dimen/dip_14"
            android:layout_marginTop="@dimen/dip_14"
            />

    </RelativeLayout>

    <View
        android:id="@+id/topbar_divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_1"
        android:layout_alignParentBottom="true"
        android:background="@color/dark5"
        android:visibility="gone" />

</RelativeLayout>
