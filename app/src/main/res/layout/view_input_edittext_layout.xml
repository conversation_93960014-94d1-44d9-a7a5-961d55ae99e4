<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="@dimen/dip_10">

    <!--<android.support.design.widget.TextInputLayout-->
        <!--android:id="@+id/input_layout"-->
        <!--android:layout_width="match_parent"-->
        <!--android:layout_height="wrap_content"-->
        <!--android:layout_centerVertical="true"-->
        <!--android:textColorHint="@color/font_color3"-->
        <!--android:orientation="horizontal"-->
        <!--&gt;-->

        <!--android:textCursorDrawable="@drawable/cursor_color"-->
    <!--android:theme="@style/BH_EDITTEXT_STYLE"-->
        <EditText
            android:id="@+id/input_edit"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_43"
            android:textCursorDrawable="@null"
            android:background="@drawable/input_bg"
            android:layout_marginTop="@dimen/dip_0"
            android:gravity="center_vertical"
            android:paddingLeft="@dimen/dip_8"
            android:paddingRight="@dimen/dip_60"
            android:hint="@string/string_input_please"
            android:focusable="true"
            android:inputType="text"
            android:singleLine="true"
            android:textColorHint="@color/dark50"
            android:textColor="@color/dark"
            android:textAppearance="@style/Body_Dark"/>

    <!--</android.support.design.widget.TextInputLayout>-->

    <LinearLayout
        android:id="@+id/input_action"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dip_43"
        android:layout_alignParentRight="true"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/input_clear"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:src="@drawable/btn_clear"
            android:layout_marginRight="@dimen/dip_4"
            android:padding="@dimen/dip_8"
            android:gravity="center"
            android:visibility="gone" />

        <CheckBox
            android:id="@+id/input_show"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:background="@null"
            android:padding="@dimen/dip_6"
            android:gravity="center"
            android:button="@drawable/btn_show"
            android:checked="false"
            android:visibility="gone" />
    </LinearLayout>
    <TextView
        android:id="@+id/error_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/input_edit"
        android:layout_marginLeft="@dimen/dip_3"
        android:textSize="@dimen/font_12"
        android:textColor="@color/red"
        android:text=""
        android:visibility="gone"
        />
</RelativeLayout>