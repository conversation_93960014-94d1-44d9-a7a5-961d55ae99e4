<?xml version="1.0" encoding="utf-8"?><!--
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: pop_otc_menu_layout.xml
  ~   @Date: 19-1-15 上午11:56
  ~   @Author: ppzhao
  ~   @Description:
  ~   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="146dp"
    android:background="@mipmap/bg_pop_menu"
    android:orientation="vertical"
    android:paddingRight="@dimen/dip_5"
    android:paddingLeft="@dimen/dip_5"
    android:paddingTop="@dimen/dip_4"
    android:paddingBottom="@dimen/dip_6">


    <TextView
        android:id="@+id/btn_lever_crncy"
        android:layout_width="@dimen/dip_128"
        android:layout_height="@dimen/dip_40"
        android:background="@drawable/popup_menu_item_selector"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/dip_16"
        android:paddingEnd="@dimen/dip_16"
        android:layout_marginTop="@dimen/dip_8"
        android:text="@string/string_margin_bebt"
        android:textColor="@color/popmenu_text_color"
        android:textSize="@dimen/sp_14" />

    <!--    <View-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="@dimen/dip_1"-->
    <!--        android:background="@color/divider_line_color"-->
    <!--        />-->


    <TextView
        android:id="@+id/btn_return_crncy"
        android:layout_width="@dimen/dip_128"
        android:layout_height="@dimen/dip_40"
        android:background="@drawable/popup_menu_item_selector"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/dip_16"
        android:paddingEnd="@dimen/dip_16"
        android:text="@string/btn_margin_repay"
        android:textColor="@color/popmenu_text_color"
        android:textSize="@dimen/sp_14" />

    <!--    <View-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="@dimen/dip_1"-->
    <!--        android:background="@color/divider_line_color"-->
    <!--        />-->

    <TextView
        android:id="@+id/btn_asset_transfer"
        android:layout_width="@dimen/dip_128"
        android:layout_height="@dimen/dip_40"
        android:background="@drawable/popup_menu_item_selector"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/dip_16"
        android:paddingEnd="@dimen/dip_16"
        android:text="@string/string_margin_transfer"
        android:textColor="@color/popmenu_text_color"
        android:textSize="@dimen/sp_14" />


    <!--    <View-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="@dimen/dip_1"-->
    <!--        android:background="@color/divider_line_color"-->
    <!--        />-->


    <TextView
        android:id="@+id/btn_margin_help"
        android:layout_width="@dimen/dip_128"
        android:layout_height="@dimen/dip_40"
        android:background="@drawable/popup_menu_item_selector"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/dip_16"
        android:paddingEnd="@dimen/dip_16"
        android:text="@string/string_margin_help"
        android:textColor="@color/popmenu_text_color"
        android:textSize="@dimen/sp_14"
        android:visibility="gone" />


</LinearLayout>