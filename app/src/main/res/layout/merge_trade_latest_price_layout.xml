<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    >
    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_0.5"
        android:background="@color/divider_line_color"
        android:layout_marginTop="@dimen/dip_7"
        android:layout_marginBottom="@dimen/dip_7"
        />
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_horizontal"
        android:visibility="invisible"
        >
        <TextView
            android:id="@+id/averagePrice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/font_15"
            android:textColor="@color/green"
            android:text="@string/string_placeholder"
            />

        <TextView
            android:id="@+id/price2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dip_5"
            android:textSize="@dimen/font_12"
            android:textColor="@color/font_color2"
            android:text="@string/string_placeholder"
            />
    </LinearLayout>
    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_0.5"
        android:background="@color/divider_line_color"
        android:layout_marginTop="@dimen/dip_7"
        android:layout_marginBottom="@dimen/dip_7"
        />

</LinearLayout>