<?xml version="1.0" encoding="utf-8"?>
<io.bhex.app.skin.view.SkinPercentRelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dip_44"
    android:background="@color/white"
    android:padding="@dimen/dip_1">

    <RelativeLayout
        android:id="@+id/tab_bg"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_40"
        android:background="@drawable/bg_corner_rect_green"
        app:layout_widthPercent="50%" />

    <RelativeLayout
        android:id="@+id/tab_bid_rela"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_40"
        android:background="@color/transparent"
        app:layout_widthPercent="50%">

        <TextView
            android:id="@+id/tab_bid"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_marginLeft="@dimen/dip_5"
            android:text="@string/string_purchase"
            android:textAppearance="@style/Body_Green" />

    </RelativeLayout>


    <RelativeLayout
        android:id="@+id/tab_ask_rela"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_40"
        android:layout_toRightOf="@+id/tab_bid_rela"
        app:layout_widthPercent="50%">

        <TextView
            android:id="@+id/tab_ask"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:text="@string/string_sellout"
            android:textAppearance="@style/Body_Dark" />
    </RelativeLayout>

</io.bhex.app.skin.view.SkinPercentRelativeLayout>