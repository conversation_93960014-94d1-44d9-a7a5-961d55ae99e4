<?xml version="1.0" encoding="utf-8"?><!--
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: item_otc_msg_txt_layout.xml
  ~   @Date: 19-1-30 下午7:20
  ~   @Author: ppzhao
  ~   @Description:
  ~   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  -->

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:padding="@dimen/dip_8">

    <TextView
        android:id="@+id/positionValue"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textAppearance="@style/Caption_Grey"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:text=""
        android:visibility="visible"
        />

    <TextView
        android:id="@+id/avator"
        android:layout_width="@dimen/dip_40"
        android:layout_height="@dimen/dip_40"
        android:background="@drawable/otc_avator_bg"
        android:layout_marginTop="@dimen/dip_8"
        app:layout_constraintTop_toBottomOf="@id/positionValue"
        android:layout_marginStart="@dimen/dip_16"
        android:layout_marginEnd="@dimen/dip_16"
        android:gravity="center"
        android:text="@string/string_placeholder"
        android:textAppearance="@style/Body_White_Bold" />
    <TextView
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="@dimen/dip_40"
        app:layout_constraintTop_toBottomOf="@id/positionValue"
        android:layout_marginTop="@dimen/dip_8"
        android:padding="@dimen/dip_8"
        android:background="@color/dark5"
        android:textAppearance="@style/Body_Dark"
        android:text=""
        android:layout_marginStart="@dimen/dip_72"
        android:layout_marginEnd="@dimen/dip_72"
        />

</androidx.constraintlayout.widget.ConstraintLayout>