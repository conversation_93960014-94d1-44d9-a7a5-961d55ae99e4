<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:id="@+id/rootView"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <TextView
        android:id="@+id/tv_schedule_name"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dip_20"
        android:layout_marginStart="@dimen/dip_20"
        android:textColor="@color/dark50"
        android:textSize="@dimen/sp_13"
        android:layout_marginTop="@dimen/dip_10"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:gravity="center_vertical"
        android:textStyle="bold"
        tools:text="1/2期" />

    <TextView
        android:id="@+id/tv_repay_amount_value"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dip_20"
        android:layout_marginEnd="@dimen/dip_20"
        android:textColor="@color/dark50"
        android:textSize="@dimen/sp_13"
        android:textStyle="bold"
        android:gravity="center_vertical"
        app:layout_constraintBottom_toBottomOf="@id/tv_schedule_name"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_schedule_name"
        tools:text="10BTC" />

    <TextView
        android:id="@+id/tv_repay_date"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dip_24"
        android:layout_marginStart="@dimen/dip_20"
        android:textColor="@color/dark"
        android:textSize="@dimen/sp_14"
        android:gravity="center_vertical"
        android:layout_marginTop="@dimen/dip_4"
        app:layout_constraintTop_toBottomOf="@id/tv_schedule_name"
        app:layout_constraintStart_toStartOf="parent"
        android:textStyle="bold"
        tools:text="还款日 2019/04/07" />

    <TextView
        android:id="@+id/tv_repay_status"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dip_24"
        android:layout_marginEnd="@dimen/dip_20"
        android:textColor="@color/dark"
        android:textSize="@dimen/sp_14"
        android:gravity="center_vertical"
        android:textStyle="bold"
        android:layout_marginTop="@dimen/dip_4"
        app:layout_constraintTop_toBottomOf="@id/tv_schedule_name"
        app:layout_constraintEnd_toEndOf="parent"
        tools:text="未开始" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/app_line"
        android:layout_marginStart="@dimen/dip_20"
        android:layout_marginTop="@dimen/dip_10"
        app:layout_constraintTop_toBottomOf="@id/tv_repay_date"
        app:layout_constraintStart_toStartOf="parent"
        android:background="@color/divider_line_color" />

</androidx.constraintlayout.widget.ConstraintLayout>