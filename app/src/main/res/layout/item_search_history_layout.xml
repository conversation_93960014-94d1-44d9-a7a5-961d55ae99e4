<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">
    <TextView
        android:id="@+id/item_name"
        android:layout_width="@dimen/dip_96"
        android:layout_height="@dimen/dip_32"
        android:layout_marginTop="@dimen/dip_8"
        android:layout_marginBottom="@dimen/dip_8"
        android:maxLines="1"
        android:ellipsize="end"
        android:textAppearance="@style/Body_Dark_Bold"
        android:text="@string/string_placeholder"
        android:gravity="center"
        />

</LinearLayout>