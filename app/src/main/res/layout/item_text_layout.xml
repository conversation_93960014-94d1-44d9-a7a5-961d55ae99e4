<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: item_text_layout.xml
  ~   @Date: 19-4-15 下午5:56
  ~   @Author: ppzhao
  ~   @Description:
  ~   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  -->

<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android" android:layout_width="match_parent"
    android:layout_height="match_parent">
    <TextView
        android:id="@+id/item"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textAppearance="@style/BodyL_Blue"/>

</androidx.constraintlayout.widget.ConstraintLayout>