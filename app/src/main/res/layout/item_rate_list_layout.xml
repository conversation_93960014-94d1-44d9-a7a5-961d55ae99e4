<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dip_60"
    style="@style/account_item_style"
    >


    <TextView
        android:id="@+id/item_value"
        style="@style/account_item_name_style"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:text="" />

    <TextView
        android:id="@+id/item_tip"
        android:layout_toLeftOf="@+id/item_selected"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:visibility="gone"
        android:text=""
        android:layout_marginLeft="@dimen/app_margin"
        android:layout_marginRight="@dimen/app_margin"
        android:textColor="@color/font_color1"
        android:textSize="@dimen/font_13" />

    <ImageView
        android:id="@id/item_selected"
        style="@style/account_item_name_style"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_centerVertical="true"
        android:layout_alignParentRight="true"
        android:layout_margin="@dimen/dip_4"
        android:src="@mipmap/icon_checkbox_normal"
         />

    <View
        android:layout_alignParentBottom="true"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_1"
        android:background="@color/divider_line_color"
        />

</RelativeLayout>