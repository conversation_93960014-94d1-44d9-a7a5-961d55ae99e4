<?xml version="1.0" encoding="utf-8"?><!--
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: otc_switch_b_layout.xml
  ~   @Date: 19-1-15 下午4:50
  ~   @Author: ppzhao
  ~   @Description:
  ~   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  -->

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    >
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_corner_white"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:layout_marginRight="@dimen/app_margin_right">

    <View
        android:id="@+id/view"
        android:layout_width="@dimen/dip_1"
        android:layout_height="match_parent"
        android:background="@color/divider_line_color"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/buy"
        android:layout_width="@dimen/dip_0"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_16"
        android:paddingTop="@dimen/dip_4"
        android:paddingBottom="@dimen/dip_4"
        android:gravity="center"
        android:text="@string/string_otc_buy"
        android:textAppearance="@style/Body_Dark"
        app:layout_constraintEnd_toStartOf="@+id/view"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/sell"
        android:layout_width="@dimen/dip_0"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_16"
        android:paddingTop="@dimen/dip_4"
        android:paddingBottom="@dimen/dip_4"
        android:gravity="center"
        android:text="@string/string_otc_sell"
        android:textAppearance="@style/Body_Dark"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/view"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/buyRv"
        android:layout_width="@dimen/dip_0"
        android:layout_height="@dimen/dip_160"
        android:layout_marginStart="@dimen/dip_8"
        android:layout_marginTop="@dimen/dip_4"
        android:layout_marginEnd="@dimen/dip_8"
        android:layout_marginBottom="@dimen/dip_8"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/view"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/buy" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/sellRv"
        android:layout_width="@dimen/dip_0"
        android:layout_height="@dimen/dip_160"
        android:layout_marginStart="@dimen/dip_8"
        android:layout_marginTop="@dimen/dip_4"
        android:layout_marginEnd="@dimen/dip_8"
        android:layout_marginBottom="@dimen/dip_8"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/view"
        app:layout_constraintTop_toBottomOf="@id/sell" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>