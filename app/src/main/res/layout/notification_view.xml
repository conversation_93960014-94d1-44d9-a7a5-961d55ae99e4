<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                android:layout_width="fill_parent"
                android:layout_height="64dp">

    <ImageView
        android:id="@+id/notification_large_icon"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:scaleType="fitXY"
        android:src="@mipmap/ic_launcher"/>

    <TextView
        android:id="@+id/notification_title"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="5dp"
        android:layout_toRightOf="@+id/notification_large_icon"
        android:text="Title"
        android:textColor="@color/black"/>

    <TextView
        android:id="@+id/notification_text"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/notification_title"
        android:layout_marginLeft="10dp"
        android:layout_toRightOf="@+id/notification_large_icon"
        android:text="Message"
        android:textColor="@color/black"/>


    <TextView
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/notification_text"
        android:layout_marginLeft="10dp"
        android:layout_toRightOf="@+id/notification_large_icon"
        android:text="(嘿嘿，偷偷听告诉你，我是自定义的)"
        android:textColor="@color/black"
        android:textSize="10dp"/>

    <ImageView
        android:id="@+id/notification_small_icon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_alignParentBottom="true"
        android:layout_alignParentRight="true"
        android:layout_marginBottom="5dp"
        android:layout_marginRight="5dp"
        android:scaleType="fitXY"
        android:src="@mipmap/ic_launcher"/>

</RelativeLayout>