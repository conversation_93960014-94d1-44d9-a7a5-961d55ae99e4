<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dip_54"
    android:paddingLeft="@dimen/dip_24"
    >

    <RelativeLayout
        android:id="@+id/tab_kline"
        android:layout_width="wrap_content"
        android:layout_height="match_parent">


        <TextView
            android:id="@+id/tab_kline_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="@dimen/dip_5"
            android:text="@string/kline_fifteen_minutes"
            android:textAppearance="@style/BodyL_Blue_Bold"/>
        <ImageView
            android:id="@+id/tab_kline_selctec_icon"
            android:layout_width="@dimen/dip_12"
            android:layout_height="@dimen/dip_12"
            android:layout_centerInParent="true"
            android:layout_toRightOf="@id/tab_kline_name"
            android:src="@mipmap/icon_arrow_down_blue"
            android:layout_marginLeft="@dimen/dip_5"
            />

        <View
            android:id="@+id/tab_indicator_kline"
            android:layout_width="@dimen/dip_65"
            android:layout_height="@dimen/dip_1"
            android:layout_alignParentBottom="true"
            android:background="@color/blue" />
    </RelativeLayout>


    <RelativeLayout
        android:id="@+id/tab_depth"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_toRightOf="@+id/tab_kline"
        android:layout_marginLeft="@dimen/dip_45"
        >


        <TextView
            android:id="@+id/tab_kline_depth"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:text="@string/kline_depth"
            android:textAppearance="@style/BodyL_Dark_Bold"/>

        <View
            android:id="@+id/tab_indicator_depth"
            android:layout_width="@dimen/dip_50"
            android:layout_height="@dimen/dip_1"
            android:layout_alignParentBottom="true"
            android:visibility="gone"
            android:background="@color/blue" />
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/tab_fullscreen"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:paddingLeft="@dimen/app_paddingLeft"
        android:paddingRight="@dimen/app_paddingRight"
        android:paddingTop="@dimen/dip_2"
        android:paddingBottom="@dimen/dip_2"
        android:orientation="horizontal"
        >


        <TextView
            android:id="@+id/full_scrren_tx"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:visibility="gone"
            android:text="@string/kline_full_screen"
            android:textAppearance="@style/BodyS_Grey"/>

        <ImageView
            android:id="@+id/full_scrren_icon"
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="@dimen/dip_5"
            android:src="@mipmap/icon_full_screen" />
    </LinearLayout>

</RelativeLayout>