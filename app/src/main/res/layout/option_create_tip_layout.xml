<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#99232323" >

    <LinearLayout
        android:layout_width="300dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/update_dialog_bg"
        android:orientation="vertical" >

        <!-- Title -->

        <RelativeLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content" >


            <TextView
                android:id="@+id/dialog_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_margin="@dimen/dip_15"
                android:layout_marginTop="@dimen/dip_15"
                android:text="@string/string_option_open_tip"
                android:textAppearance="@style/BodyL_Dark_Default" />

        </RelativeLayout>

        <!-- split -->

        <!-- Content -->


                <LinearLayout
                    android:id="@+id/signup_protocol_linea"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dip_8"
                    android:layout_marginBottom="@dimen/dip_12"
                    android:layout_marginLeft="@dimen/dip_4"
                    android:layout_gravity="center_horizontal"
                    android:orientation="horizontal">

                    <CheckBox
                        android:id="@+id/option_open_checkbox"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:button="@drawable/checkbox_style"
                        android:checked="false" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/string_option_create_tip"
                        android:textAppearance="@style/Body_Dark_Default" />

                </LinearLayout>

        <!-- Ignore CheckBox -->


        <!-- OK&Cancel Button -->

        <View
            android:layout_width="fill_parent"
            android:layout_height="8dip"
            android:background="@color/bgColor_divier" />

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content" >
            <Button
                android:id="@+id/update_id_ok"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/btn_right_selector"
                android:focusable="true"
                android:gravity="center"
                android:padding="12dp"
                android:textColor="#000000"
                android:textSize="16sp" />

            <View
                android:layout_width="0.5dip"
                android:layout_height="fill_parent"
                android:layout_gravity="center_horizontal"
                android:background="#e1dede" />

            <Button
                android:id="@+id/update_id_cancel"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/btn_left_selector"
                android:focusable="true"
                android:gravity="center"
                android:padding="12dp"
                android:textColor="#000000"
                android:textSize="16sp" />
        </LinearLayout>
    </LinearLayout>

</RelativeLayout>