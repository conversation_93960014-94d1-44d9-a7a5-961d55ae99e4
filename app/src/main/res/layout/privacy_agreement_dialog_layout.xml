<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/color_bg_1"
    android:orientation="vertical"
    >

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/dip_10"
        android:paddingBottom="@dimen/dip_10"
        android:gravity="center"
        android:layout_gravity="center_horizontal"
        android:textAppearance="@style/BodyL_Dark"
        android:textStyle="bold"
        android:maxLines="1"
        android:text="@string/string_reminder"
        />

    <ScrollView
        android:id="@+id/contentLinear"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        >
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            >
            <TextView
                android:id="@+id/msg"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minHeight="@dimen/dip_60"
                android:layout_marginTop="@dimen/dip_20"
                android:layout_gravity="center_horizontal"
                android:textAppearance="@style/Body_Dark"
                android:text="@string/string_protocol_tips_content"
                android:paddingLeft="@dimen/app_paddingLeft"
                android:paddingRight="@dimen/app_paddingRight" />
        </LinearLayout>
    </ScrollView>

    <LinearLayout
        android:id="@+id/signup_protocol_linea"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:layout_marginRight="@dimen/app_margin_right"
        android:layout_marginTop="@dimen/dip_12"
        android:layout_marginBottom="@dimen/dip_20"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/string_protocol_go_read"
            android:textAppearance="@style/Body_Dark" />

        <TextView
            android:id="@+id/privacy_protocol"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dip_5"
            android:text="@string/string_privacy_protocol"
            android:textColor="@color/blue"
            android:textSize="@dimen/font_13" />

        <TextView
            android:id="@+id/signup_protocol"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dip_5"
            android:text="@string/string_signup_protocol"
            android:textColor="@color/blue"
            android:textSize="@dimen/font_13" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_0.5"
        android:background="@color/divider_line_color"
        />
    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal"
        >

        <Button
            android:id="@+id/btnCancel"
            android:layout_width="fill_parent"
            android:layout_height="@dimen/dip_45"
            android:textAppearance="@style/Body_Dark"
            android:text="@string/string_disagree_exit"
            android:layout_weight="1"
            android:background="@null" />
        <View
            android:layout_width="@dimen/dip_0.5"
            android:layout_height="match_parent"
            android:background="@color/divider_line_color"
            />

        <Button
            android:id="@+id/btnConfirm"
            android:layout_width="fill_parent"
            android:layout_height="@dimen/dip_45"
            android:textAppearance="@style/Body_Blue"
            android:text="@string/string_agree"
            android:layout_weight="1"
            android:background="@null" />
    </LinearLayout>

</LinearLayout>
