<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    >

    <com.bhex.kline.widget.tab.KLineTabLayout
        android:id="@+id/tab_kline"
        android:layout_width="match_parent"
        android:layout_height="42dp"
        android:layout_alignParentStart="true"
        android:background="@color/tab_kline_background"
        android:layout_toLeftOf="@+id/iv_full"
        app:textNormalColor="@color/kline_tab_normal"
        app:textPressColor="#3375E0"
        app:normalFillColor="#00000000"
        app:pressFillColor="#00000000"
        />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_full"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:src="@mipmap/ic_full"
        android:layout_alignParentEnd="true"
        android:paddingEnd="15dp"
        android:paddingTop="8dp"
        android:paddingBottom="8dp"
        />


</RelativeLayout>