<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginTop="@dimen/dip_0.5"
        android:background="@color/color_bg_2"
        >
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_horizontal"
            android:layout_marginTop="@dimen/dip_20"
            >

            <TextView
                android:id="@+id/detail_price_mode"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/BodyL_Dark_Bold"
                />
            <TextView
                android:id="@+id/detail_buymode"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dip_5"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/BodyL_Dark_Bold"
                />

        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_horizontal"
            android:layout_marginTop="@dimen/dip_10"
            >

            <TextView
                android:id="@+id/detail_coinpair"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/H3_Dark"
                />

        </LinearLayout>
        <TextView
            android:id="@+id/detail_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dip_10"
            android:layout_marginLeft="@dimen/dip_5"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Grey"
            />
        <RelativeLayout
            style="@style/config_item_style"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_20"
            >

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/font_color2"
                android:textSize="@dimen/font_15"
                android:text="@string/string_price" />
            <TextView
                android:id="@+id/detail_order_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:textColor="@color/font_color1"
                android:textSize="@dimen/font_15"
                android:text="@string/string_placeholder" />

        </RelativeLayout>
        <RelativeLayout
            style="@style/config_item_style"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_10"
            >

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/font_color2"
                android:textSize="@dimen/font_15"
                android:text="@string/string_order_deal_average_price" />
            <TextView
                android:id="@+id/detail_order_deal_average_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:textColor="@color/font_color1"
                android:textSize="@dimen/font_15"
                android:text="@string/string_placeholder" />

        </RelativeLayout>
        <RelativeLayout
            style="@style/config_item_style"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_10"
            >

            <TextView
                android:id="@+id/detail_order_entrust_amount_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/font_color2"
                android:textSize="@dimen/font_15"
                android:text="@string/string_order_entrust_amount" />
            <TextView
                android:id="@+id/detail_order_entrust_amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:textColor="@color/font_color1"
                android:textSize="@dimen/font_15"
                android:text="@string/string_placeholder" />

        </RelativeLayout>
        <RelativeLayout
            style="@style/config_item_style"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_10"
            >

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/font_color2"
                android:textSize="@dimen/font_15"
                android:text="@string/string_order_deal_amount" />
            <TextView
                android:id="@+id/detail_order_deal_amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:textColor="@color/font_color1"
                android:textSize="@dimen/font_15"
                android:text="@string/string_placeholder" />

        </RelativeLayout>
        <RelativeLayout
            style="@style/config_item_style"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_10"
            >

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/font_color2"
                android:textSize="@dimen/font_15"
                android:text="@string/string_order_total_deal_money" />
            <TextView
                android:id="@+id/detail_order_total_deal_money"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:textColor="@color/font_color1"
                android:textSize="@dimen/font_15"
                android:text="@string/string_placeholder" />

        </RelativeLayout>
        <RelativeLayout
            style="@style/config_item_style"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:layout_marginTop="@dimen/dip_10"
            android:layout_marginBottom="@dimen/dip_20"
            >

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/font_color2"
                android:textSize="@dimen/font_15"
                android:text="@string/string_order_total_fee" />
            <TextView
                android:id="@+id/detail_order_total_fee"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:textColor="@color/font_color1"
                android:textSize="@dimen/font_15"
                android:text="@string/string_placeholder" />

        </RelativeLayout>
    </LinearLayout>
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/color_bg_2"
        android:layout_marginTop="@dimen/dip_5"
        android:layout_marginBottom="@dimen/dp_0.5"
        android:padding="@dimen/app_padding"
        android:textAppearance="@style/Body_Dark_Bold"
        android:text="@string/string_deal_detail"
        />

</LinearLayout>