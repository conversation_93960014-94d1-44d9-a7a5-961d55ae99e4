<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_1"
        android:background="@color/divider_line_color" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <io.bhex.app.skin.view.SkinPercentRelativeLayout
            xmlns:app="http://schemas.android.com/apk/res-auto"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_45"
            android:layout_weight="1"
            android:background="@color/color_bg_2">

            <TextView
                android:id="@+id/view_start"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <RelativeLayout
                android:id="@+id/tab_land_time_rela"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_toRightOf="@+id/view_start"
                app:layout_widthPercent="10%">


                <TextView
                    android:id="@+id/tab_land_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:gravity="center"
                    android:text="@string/kline_minutes"
                    android:textAppearance="@style/BodyS_Dark" />

                <View
                    android:id="@+id/tab_land_indicator_time"
                    android:layout_width="@dimen/dip_50"
                    android:layout_height="@dimen/dip_2"
                    android:layout_alignParentBottom="true"
                    android:background="@color/blue"
                    android:visibility="gone" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/tab_land_1m_rela"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_toRightOf="@+id/tab_land_time_rela"
                app:layout_widthPercent="10%">


                <TextView
                    android:id="@+id/tab_land_1m"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:gravity="center"
                    android:text="@string/kline_one_minute"
                    android:textAppearance="@style/BodyS_Dark" />

                <View
                    android:id="@+id/tab_land_indicator_1m"
                    android:layout_width="@dimen/dip_50"
                    android:layout_height="@dimen/dip_2"
                    android:layout_alignParentBottom="true"
                    android:background="@color/blue"
                    android:visibility="gone" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/tab_land_5m_rela"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_toRightOf="@+id/tab_land_1m_rela"
                app:layout_widthPercent="10%">


                <TextView
                    android:id="@+id/tab_land_5m"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:gravity="center"
                    android:text="@string/kline_five_minutes"
                    android:textAppearance="@style/BodyS_Dark" />

                <View
                    android:id="@+id/tab_land_indicator_5m"
                    android:layout_width="@dimen/dip_50"
                    android:layout_height="@dimen/dip_2"
                    android:layout_alignParentBottom="true"
                    android:background="@color/blue"
                    android:visibility="gone" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/tab_land_15m_rela"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_toRightOf="@+id/tab_land_5m_rela"
                app:layout_widthPercent="10%">


                <TextView
                    android:id="@+id/tab_land_15m"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:gravity="center"
                    android:text="@string/kline_fifteen_minutes"
                    android:textAppearance="@style/BodyS_Dark" />

                <View
                    android:id="@+id/tab_land_indicator_15m"
                    android:layout_width="@dimen/dip_50"
                    android:layout_height="@dimen/dip_2"
                    android:layout_alignParentBottom="true"
                    android:background="@color/blue"
                    android:visibility="gone" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/tab_land_30m_rela"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_toRightOf="@+id/tab_land_15m_rela"
                app:layout_widthPercent="10%">


                <TextView
                    android:id="@+id/tab_land_30m"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:gravity="center"
                    android:text="@string/kline_thirty_minutes"
                    android:textAppearance="@style/BodyS_Dark" />

                <View
                    android:id="@+id/tab_land_indicator_30m"
                    android:layout_width="@dimen/dip_50"
                    android:layout_height="@dimen/dip_2"
                    android:layout_alignParentBottom="true"
                    android:background="@color/blue"
                    android:visibility="gone" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/tab_land_minutes_rela"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_toRightOf="@+id/tab_land_time_rela"
                android:visibility="gone"
                app:layout_widthPercent="16%">

                <TextView
                    android:id="@+id/tab_land_minutes"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:layout_marginLeft="@dimen/dip_5"
                    android:text="@string/kline_fifteen_minutes"
                    android:textAppearance="@style/BodyS_Dark" />

                <ImageView
                    android:id="@+id/tab_land_selctec_icon"
                    android:layout_width="@dimen/dip_12"
                    android:layout_height="@dimen/dip_12"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="@dimen/dip_5"
                    android:layout_toRightOf="@+id/tab_land_minutes"
                    android:src="@mipmap/icon_arrow_up_blue" />

                <View
                    android:id="@+id/tab_land_indicator_minutes"
                    android:layout_width="@dimen/dip_65"
                    android:layout_height="@dimen/dip_2"
                    android:layout_alignParentBottom="true"
                    android:background="@color/blue"
                    android:visibility="gone" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/tab_land_hour_rela"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_toRightOf="@+id/tab_land_30m_rela"
                app:layout_widthPercent="10%">


                <TextView
                    android:id="@+id/tab_land_hour"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:gravity="center"
                    android:text="@string/kline_one_hour"
                    android:textAppearance="@style/BodyS_Dark" />

                <View
                    android:id="@+id/tab_land_indicator_hour"
                    android:layout_width="@dimen/dip_50"
                    android:layout_height="@dimen/dip_2"
                    android:layout_alignParentBottom="true"
                    android:background="@color/blue"
                    android:visibility="gone" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/tab_land_day_rela"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_toRightOf="@+id/tab_land_hour_rela"
                app:layout_widthPercent="10%">


                <TextView
                    android:id="@+id/tab_land_day"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:gravity="center"
                    android:text="@string/kline_days"
                    android:textAppearance="@style/BodyS_Dark" />

                <View
                    android:id="@+id/tab_land_indicator_day"
                    android:layout_width="@dimen/dip_50"
                    android:layout_height="@dimen/dip_2"
                    android:layout_alignParentBottom="true"
                    android:background="@color/blue"
                    android:visibility="gone" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/tab_land_week_rela"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_toRightOf="@+id/tab_land_day_rela"
                app:layout_widthPercent="10%">


                <TextView
                    android:id="@+id/tab_land_week"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:gravity="center"
                    android:text="@string/kline_weeks"
                    android:textAppearance="@style/BodyS_Dark" />

                <View
                    android:id="@+id/tab_land_indicator_week"
                    android:layout_width="@dimen/dip_50"
                    android:layout_height="@dimen/dip_2"
                    android:layout_alignParentBottom="true"
                    android:background="@color/blue"
                    android:visibility="gone" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/tab_land_month_rela"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_toRightOf="@+id/tab_land_week_rela"
                app:layout_widthPercent="10%">


                <TextView
                    android:id="@+id/tab_land_month"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:gravity="center"
                    android:text="@string/kline_months"
                    android:textAppearance="@style/BodyS_Dark" />

                <View
                    android:id="@+id/tab_land_indicator_month"
                    android:layout_width="@dimen/dip_50"
                    android:layout_height="@dimen/dip_2"
                    android:layout_alignParentBottom="true"
                    android:background="@color/blue"
                    android:visibility="gone" />
            </RelativeLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_widthPercent="2%"
                android:layout_toRightOf="@+id/ta_land_month_rela" />
        </io.bhex.app.skin.view.SkinPercentRelativeLayout>

        <RelativeLayout
            android:id="@+id/tab_land_index_rela"
            android:layout_width="@dimen/dip_75"
            android:layout_height="@dimen/dip_45">

            <LinearLayout

                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <TextView
                    android:id="@+id/tab_land_index"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/index"
                    android:textAppearance="@style/BodyS_Dark" />

                <ImageView
                    android:id="@+id/tab_land_index_icon"
                    android:layout_width="@dimen/dip_16"
                    android:layout_height="@dimen/dip_16"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="@dimen/dip_4"
                    android:layout_toRightOf="@+id/tab_land_index"
                    android:src="@mipmap/icon_arrow_up_black" />
            </LinearLayout>
        </RelativeLayout>
    </LinearLayout>
</LinearLayout>