<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="#0B1622">


    <com.google.android.flexbox.FlexboxLayout
        android:id="@+id/gl_indicator"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:flexWrap="wrap"
        >

    </com.google.android.flexbox.FlexboxLayout>

    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="left"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="10dp">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_reset"
            android:layout_width="100dp"
            android:layout_height="30dp"
            android:textSize="15sp"
            android:textColor="#5C698C"
            android:gravity="center"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:text="@string/string_reset"
            android:drawableStart="@drawable/ic_reset"
            />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_confrim"
            android:layout_width="100dp"
            android:layout_height="30dp"
            android:textSize="15sp"
            android:textColor="#5C698C"
            android:gravity="center"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:text="@string/string_sure"
            android:visibility="invisible"
            />
    </LinearLayout>

    <View
        android:layout_width="wrap_content"
        android:layout_height="0.5dp"
        android:background="#283345" />

    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="15dp"
        android:textSize="15sp"
        android:textColor="#D8DCE5"
        android:text="@string/string_brief_introduction" />


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_introduction"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="4dp"
        android:layout_marginBottom="13dp"
        android:textSize="14sp"
        android:textColor="#5C698C"
        android:text="MA指标由一段时间内的收盘价之和取算数平均数后连线，可抚平短期波动，反映出长期趋势或周期" />
</LinearLayout>
