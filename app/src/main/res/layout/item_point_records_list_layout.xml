<?xml version="1.0" encoding="utf-8"?><!--
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: item_point_records_list_layout.xml
  ~   @Date: 18-12-2 下午10:13
  ~   @Author: ppzhao
  ~   @Description:
  ~   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  -->

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dip_56"
    android:paddingLeft="@dimen/app_paddingLeft"
    >

    <TextView
        android:id="@+id/item_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_8"
        android:text="@string/string_placeholder"
        android:textAppearance="@style/Body_Dark" />

    <TextView
        android:id="@+id/item_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/item_name"
        android:text="@string/string_placeholder"
        android:layout_marginTop="@dimen/dip_8"
        android:textAppearance="@style/Caption_Grey" />
    <TextView
        android:id="@+id/item_amount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_marginRight="@dimen/app_margin_right"
        android:layout_marginTop="@dimen/dip_8"
        android:text="@string/string_placeholder"
        android:textAppearance="@style/Body_Dark" />

    <TextView
        android:id="@+id/item_gift_amount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_marginRight="@dimen/app_margin_right"
        android:layout_below="@id/item_name"
        android:text=""
        android:layout_marginTop="@dimen/dip_8"
        android:textAppearance="@style/Caption_Grey" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_1"
        android:background="@color/divider_line_color"
        android:layout_alignParentBottom="true"
        />

</RelativeLayout>