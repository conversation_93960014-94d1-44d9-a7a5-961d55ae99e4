<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingLeft="@dimen/app_paddingLeft"
    >
    <TextView
        android:id="@+id/amount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textAppearance="@style/Body_Dark"
        android:layout_marginTop="@dimen/app_margin_top"
        />
    <TextView
        android:id="@+id/desc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_marginRight="@dimen/app_margin_right"
        android:textAppearance="@style/Body_Dark"
        android:layout_marginTop="@dimen/app_margin_top"
        />

    <TextView
        android:id="@+id/time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/amount"
        android:textAppearance="@style/Caption_Grey"
        android:layout_marginBottom="@dimen/dip_16"
        />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_1"
        android:background="@color/divider_line_color"
        android:layout_below="@id/time"
        />
</RelativeLayout>