<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:orientation="vertical"
    >

    <io.bhex.app.view.TopBar
        android:id="@+id/topBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:left_text=""
        app:title_text="@string/title_order" />

    <LinearLayout
        android:id="@+id/linear"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:layout_marginTop="@dimen/dip_1"
        >
        <View
            android:layout_alignParentBottom="true"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_1"
            android:background="@color/divider_line_color" />

        <RelativeLayout
            android:id="@+id/trade_orders_layout"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_60"
            style="@style/account_item_style"
            >


            <TextView
                style="@style/account_item_name_style"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="@string/string_trade_orders" />
            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:background="@mipmap/icon_arrow_right" />


        </RelativeLayout>
        <View
            android:id="@+id/trade_orders_layout_divider"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_1"
            android:layout_marginLeft="@dimen/app_margin"
            android:visibility="visible"
            android:background="@color/divider_line_color" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_60"
            android:id="@+id/margin_orders_layout"
            style="@style/account_item_style"
            >

            <TextView
                style="@style/account_item_name_style"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="@string/string_asset_margin_order" />
            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:background="@mipmap/icon_arrow_right" />

        </RelativeLayout>
        <View
            android:id="@+id/margin_orders_layout_divider"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_1"
            android:layout_marginLeft="@dimen/app_margin"
            android:background="@color/divider_line_color" />
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_60"
            android:id="@+id/futures_orders_layout"
            style="@style/account_item_style"
            android:visibility="visible"
            >


            <TextView
                style="@style/account_item_name_style"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="@string/string_futures_orders" />
            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:background="@mipmap/icon_arrow_right" />

        </RelativeLayout>
        <View
            android:id="@+id/futures_orders_layout_divider"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_1"
            android:layout_marginLeft="@dimen/app_margin"
            android:background="@color/divider_line_color" />
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_60"
            android:id="@+id/option_orders_layout"
            style="@style/account_item_style"
            android:visibility="visible"
            >


            <TextView
                style="@style/account_item_name_style"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="@string/string_option_orders" />
            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:background="@mipmap/icon_arrow_right" />

        </RelativeLayout>
        <View
            android:id="@+id/option_orders_layout_divider"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_1"
            android:layout_marginLeft="@dimen/app_margin"
            android:background="@color/divider_line_color" />
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_60"
            android:id="@+id/otc_orders_layout"
            style="@style/account_item_style"
            >


            <TextView
                style="@style/account_item_name_style"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="@string/string_otc_orders" />
            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:background="@mipmap/icon_arrow_right" />

        </RelativeLayout>
        <View
            android:id="@+id/otc_orders_layout_divider"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_1"
            android:layout_marginLeft="@dimen/app_margin"
            android:background="@color/divider_line_color" />
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_60"
            android:id="@+id/coinplus_orders_layout"
            style="@style/account_item_style"
            >

            <TextView
                style="@style/account_item_name_style"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="@string/string_asset_coinplus_order" />
            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:background="@mipmap/icon_arrow_right" />

        </RelativeLayout>
        <View
            android:id="@+id/coinplus_orders_layout_divider"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_1"
            android:layout_marginLeft="@dimen/app_margin"
            android:background="@color/divider_line_color" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_60"
            android:id="@+id/staking_orders_layout"
            style="@style/account_item_style"
            >

            <TextView
                style="@style/account_item_name_style"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="@string/string_staking_order" />
            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:background="@mipmap/icon_arrow_right" />

        </RelativeLayout>
        <View
            android:id="@+id/staking_orders_layout_divider"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_1"
            android:layout_marginLeft="@dimen/app_margin"
            android:background="@color/divider_line_color" />

    </LinearLayout>


</LinearLayout>