<?xml version="1.0" encoding="utf-8"?>
<!--
  ~
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: layout_alertview.xml
  ~   @Date: 11/29/18 3:21 PM
  ~   @Author: chenjun
  ~   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  ~
  -->

<FrameLayout
    android:id="@+id/outmost_container"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clickable="true"
    android:background="@color/bgColor_overlay">
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:background="@color/bgColor_overlay">


        <FrameLayout
            android:id="@+id/content_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            >
        </FrameLayout>

    </FrameLayout>

</FrameLayout>