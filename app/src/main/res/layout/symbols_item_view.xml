<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/itemView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:background="@drawable/item_style">

        <LinearLayout
            android:id="@+id/linear1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <TextView
                android:id="@+id/item_coinpair"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/Body_Dark_Bold" />

            <TextView
                android:id="@+id/item_amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_5"
                android:ellipsize="end"
                android:visibility="gone"
                android:maxLines="1"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/BodyS_Grey" />
            <TextView
                android:id="@+id/item_price1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dip_4"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/Body_Green_Bold" />

            <TextView
                android:id="@+id/item_range_ratio"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dip_2"
                android:gravity="center"
                android:maxLines="1"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/BodyS_Green"/>

            <TextView
                android:id="@+id/item_price2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dip_2"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/BodyS_Grey" />

        </LinearLayout>


</RelativeLayout>