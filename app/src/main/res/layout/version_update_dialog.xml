<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    >

    <RelativeLayout
        android:layout_width="@dimen/dip_320"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:orientation="vertical" >

        <!-- Title -->

        <RelativeLayout
            android:id="@+id/titleRela"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            >

            <ImageView
                android:layout_width="320dp"
                android:layout_height="170dp"
                android:layout_centerVertical="true"
                android:scaleType="fitXY"
                android:src="@mipmap/bg_update" />

            <TextView
                android:id="@+id/dialog_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/color_bg_2"
                android:layout_marginTop="160dp"
                android:paddingLeft="@dimen/app_paddingLeft"
                android:text="@string/string_version_find_new"
                android:textStyle="normal"
                android:textAppearance="@style/H4_Blue"
                />

            <Button
                android:id="@+id/update_id_cancel"
                android:layout_width="@dimen/dip_30"
                android:layout_height="@dimen/dip_30"
                android:padding="@dimen/dip_16"
                android:layout_marginTop="66dp"
                android:background="@mipmap/icon_close_dark"
                android:layout_alignParentRight="true"
                android:layout_marginRight="10dp"
                android:focusable="true"
                android:visibility="visible" />
        </RelativeLayout>

        <io.bhex.app.view.WrapScrollView
            android:id="@+id/contentSV"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/titleRela"
            android:background="@color/color_bg_2"
            android:fillViewport="true"
            android:overScrollMode="never"
            android:visibility="visible">


            <TextView
                android:id="@+id/update_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/color_bg_2"
                android:visibility="visible"
                android:paddingTop="@dimen/dip_16"
                android:paddingStart="@dimen/dip_16"
                android:paddingEnd="@dimen/dip_16"
                android:paddingBottom="@dimen/dip_32"
                android:lineSpacingExtra="@dimen/dip_8"
                android:text=""
                android:focusable="true"
                android:textAppearance="@style/Body_Grey"/>
        </io.bhex.app.view.WrapScrollView>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_80"
            android:gravity="center_vertical"
            android:orientation="vertical"
            android:background="@color/color_bg_2"
            android:layout_below="@id/contentSV"
            >

            <Button
                android:id="@+id/update_id_ok"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dip_48"
                android:layout_marginTop="@dimen/dip_8"
                android:layout_marginLeft="@dimen/app_margin_left"
                android:layout_marginRight="@dimen/app_margin_right"
                android:layout_marginBottom="@dimen/dip_16"
                android:background="@drawable/btn_corner"
                android:focusable="true"
                android:textColor="@color/color_white"
                android:textAppearance="@style/BodyL_White"/>
        </LinearLayout>

    </RelativeLayout>

</RelativeLayout>