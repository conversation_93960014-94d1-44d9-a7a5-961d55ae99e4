<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/tagName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_5"
        android:layout_marginEnd="@dimen/dip_12"
        android:padding="@dimen/dip_4"
        android:background="@drawable/bg_corner_rect_green"
        android:text="@string/string_placeholder"
        android:singleLine="true"
        android:textAppearance="@style/BodyS_Dark" />

</LinearLayout>