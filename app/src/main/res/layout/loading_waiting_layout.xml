<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    >

    <RelativeLayout
        android:id="@+id/layout_popup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_marginLeft="@dimen/app_margin"
        android:layout_marginRight="@dimen/app_margin"
        android:background="@null"
        android:padding="@dimen/app_padding"
        android:gravity="center"
        android:orientation="vertical">

        <ProgressBar
            android:id="@+id/bar_loading"
            style="@style/loading_style"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_gravity="center"
            android:gravity="center"
            android:visibility="visible" />

        <TextView
            android:id="@+id/loading_hint_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/bar_loading"
            android:visibility="gone"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/dip_10"
            android:text="@string/string_loading_hint_text"
            android:textAppearance="@style/BodyS_Dark"/>
    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>