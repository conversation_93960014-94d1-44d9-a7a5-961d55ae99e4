<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/item_style"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/account_auth_rela"
        style="@style/config_item_style"
        android:layout_width="fill_parent"
        android:layout_height="@dimen/dip_56"
        >
        <TextView
            android:id="@+id/token_name"
            style="@style/account_item_name_style"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="@string/string_placeholder" />

        <ImageView
            android:id="@+id/arrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:background="@mipmap/icon_line_right" />

        <TextView
            android:id="@+id/account_auth_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginRight="@dimen/dip_5"
            android:layout_toLeftOf="@+id/arrow"
            android:textAppearance="@style/BodyS_Grey"
            android:text="@string/string_placeholder" />

    </RelativeLayout>

    <View
        android:id="@+id/item_divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_1"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:background="@color/divider_line_color"
        />
</LinearLayout>