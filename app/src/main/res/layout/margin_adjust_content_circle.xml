<?xml version="1.0" encoding="utf-8"?><!--
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: margin_adjust_content_circle.xml
  ~   @Date: 19-7-29 下午7:44
  ~   @Author: ppzhao
  ~   @Description:
  ~   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_dialog"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/app_margin_right"
        android:layout_marginRight="@dimen/app_title_icon_margin">


        <ImageView
            android:id="@+id/close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginTop="@dimen/dip_8"
            android:padding="@dimen/dip_8"
            android:src="@mipmap/icon_close" />
        <TextView
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:layout_marginTop="@dimen/dip_16"
            android:paddingBottom="@dimen/dip_8"
            android:layout_alignBaseline="@id/close"
            android:layout_toLeftOf="@id/close"
            android:layout_centerHorizontal="true"
            android:textAppearance="@style/H4_Dark"
            android:text="@string/string_adjust_margin"/>

        <com.flyco.tablayout.SegmentTabLayout
            android:id="@+id/segmentTab"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_35"
            android:layout_marginTop="@dimen/dip_16"
            android:layout_below="@id/title"
            android:layout_gravity="center_horizontal"
            app:tl_tab_space_equal="true"
            app:tl_bar_color="@color/white"
            app:tl_indicator_color="@color/blue"
            app:tl_indicator_corner_radius="2dp"
            app:tl_tab_padding="20dp" />

    </RelativeLayout>


    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_1"
        android:layout_marginLeft="@dimen/app_margin"
        android:layout_marginRight="@dimen/app_margin"
        android:background="@color/dark5" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/app_margin_right"
        android:layout_marginStart="@dimen/app_margin_left"
        android:layout_marginTop="@dimen/dip_16"
        android:layout_marginBottom="@dimen/app_margin_bottom">
        <TextView
            android:id="@+id/title_contract_name"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintWidth_percent="0.5"
            android:text="@string/string_contract_point"
            android:textAppearance="@style/BodyS_Grey"
            />
        <LinearLayout
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toEndOf="@id/title_contract_name"
            app:layout_constraintWidth_percent="0.5"
            android:orientation="horizontal">
            <TextView
                android:id="@+id/value_contract_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/BodyS_Dark"
                />
            <TextView
                android:id="@+id/value_contract_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dip_4"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/BodyS_Dark"
                />
        </LinearLayout>
        <TextView
            android:id="@+id/title_contract_hold"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintWidth_percent="0.5"
            app:layout_constraintTop_toBottomOf="@id/title_contract_name"
            android:layout_marginTop="@dimen/dip_12"
            android:text="@string/string_futures_hold_amount_with_unit"
            android:textAppearance="@style/BodyS_Grey"
            />
        <TextView
            android:id="@+id/value_contract_hold"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/title_contract_hold"
            app:layout_constraintWidth_percent="0.5"
            app:layout_constraintTop_toBottomOf="@id/title_contract_name"
            android:layout_marginTop="@dimen/dip_12"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyS_Dark"
            />
        <TextView
            android:id="@+id/title_hold_margin"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintWidth_percent="0.5"
            app:layout_constraintTop_toBottomOf="@id/title_contract_hold"
            android:layout_marginTop="@dimen/dip_12"
            android:text="@string/string_hold_position_margin_point"
            android:textAppearance="@style/BodyS_Grey"
            />
        <TextView
            android:id="@+id/value_hold_margin"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/title_hold_margin"
            app:layout_constraintWidth_percent="0.5"
            app:layout_constraintTop_toBottomOf="@id/title_contract_hold"
            android:layout_marginTop="@dimen/dip_12"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyS_Dark"
            />
        <TextView
            android:id="@+id/title_max_increase_margin"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintWidth_percent="0.5"
            app:layout_constraintTop_toBottomOf="@id/title_hold_margin"
            android:layout_marginTop="@dimen/dip_12"
            android:text="@string/string_max_increase_margin_point"
            android:textAppearance="@style/BodyS_Grey"
            />
        <TextView
            android:id="@+id/value_max_increase_margin"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/title_max_increase_margin"
            app:layout_constraintWidth_percent="0.5"
            app:layout_constraintTop_toBottomOf="@id/title_hold_margin"
            android:layout_marginTop="@dimen/dip_12"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyS_Dark"
            />
        <TextView
            android:id="@+id/title_increase_margin"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintWidth_percent="0.5"
            app:layout_constraintTop_toBottomOf="@id/title_max_increase_margin"
            android:layout_marginTop="@dimen/dip_12"
            android:text="@string/string_increase_margin_point"
            android:textAppearance="@style/BodyS_Grey"
            />
        <RelativeLayout
            android:id="@+id/input_increase_margin_rela"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_8"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:background="@drawable/bg_corner_gray_line"
            app:layout_constraintTop_toBottomOf="@id/title_increase_margin">


            <TextView
                android:id="@+id/inputMarginUnit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:text="@string/string_placeholder"
                android:layout_marginRight="@dimen/dip_12"
                android:layout_alignBaseline="@id/input_increase_margin"
                android:textAppearance="@style/Body_Dark"/>

            <EditText
                android:id="@+id/input_increase_margin"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dip_40"
                android:layout_toLeftOf="@id/inputMarginUnit"
                android:hint="@string/string_margin"
                android:maxLines="1"
                android:paddingLeft="@dimen/dip_8"
                android:paddingRight="@dimen/dip_10"
                android:textAppearance="@style/BodyS_Dark"
                android:textColor="@color/dark"
                android:background="@null"
                />

        </RelativeLayout>

        <TextView
            android:id="@+id/title_force_close_price"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintWidth_percent="0.5"
            app:layout_constraintTop_toBottomOf="@id/input_increase_margin_rela"
            android:layout_marginTop="@dimen/dip_12"
            android:text="@string/string_force_close_price_point"
            android:visibility="gone"
            android:textAppearance="@style/BodyS_Grey"
            />
        <TextView
            android:id="@+id/value_force_close_price"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/title_force_close_price"
            app:layout_constraintWidth_percent="0.5"
            app:layout_constraintTop_toBottomOf="@id/input_increase_margin_rela"
            android:layout_marginTop="@dimen/dip_12"
            android:text="@string/string_placeholder"
            android:visibility="gone"
            android:textAppearance="@style/BodyS_Dark"
            />

        <TextView
            android:id="@+id/btnSure"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_35"
            android:gravity="center"
            app:layout_constraintTop_toBottomOf="@id/title_force_close_price"
            android:layout_marginTop="@dimen/dip_20"
            android:background="@drawable/btn_corner"
            android:text="@string/string_sure"
            style="@style/btn_style"
            />

    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>