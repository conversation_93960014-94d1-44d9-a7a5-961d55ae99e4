<?xml version="1.0" encoding="utf-8"?><!--
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: item_otc_filter_item_list_layout.xml
  ~   @Date: 19-4-16 下午3:02
  ~   @Author: ppzhao
  ~   @Description:
  ~   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <CheckBox
        android:id="@+id/item_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="@dimen/dip_30"
        android:layout_margin="@dimen/dip_8"
        android:background="@drawable/otc_btn_filter_style"
        android:button="@null"
        android:checked="true"
        android:gravity="center"
        android:text="@string/string_placeholder"
        android:textColor="@color/otc_filter_text_color"
        android:textSize="@dimen/font_13" />

</LinearLayout>