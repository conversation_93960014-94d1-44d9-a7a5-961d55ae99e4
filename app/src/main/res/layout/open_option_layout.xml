<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#99232323" >

    <LinearLayout
        android:layout_width="300dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/update_dialog_bg"
        android:orientation="vertical" >

        <!-- Title -->

        <RelativeLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content" >


            <TextView
                android:id="@+id/dialog_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_margin="@dimen/dip_15"
                android:layout_marginTop="@dimen/dip_15"
                android:text="@string/string_option_open_tip"
                android:textAppearance="?android:attr/textAppearanceLarge"
                android:textColor="#000000"
                android:textSize="18sp" />

            <Button
                android:id="@+id/umeng_update_id_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="10dp"
                android:focusable="true"
                android:visibility="gone" />
        </RelativeLayout>

        <!-- split -->

        <!-- Content -->

        <ScrollView
            android:layout_width="fill_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:scrollbars="none"
            android:padding="6dp"
            android:visibility="visible">

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical" >

                <LinearLayout
                    android:id="@+id/signup_protocol_linea"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dip_8"
                    android:layout_marginBottom="@dimen/dip_12"
                    android:layout_gravity="center_horizontal"
                    android:orientation="horizontal">

                    <CheckBox
                        android:id="@+id/option_open_checkbox"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/dip_4"
                        android:button="@drawable/checkbox_style"
                        android:checked="false" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/string_read_agree"
                        android:textColor="@color/font_color2"
                        android:textSize="@dimen/font_13" />
                    <TextView
                        android:id="@+id/signup_protocol"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/dip_50"
                        android:layout_marginLeft="4dp"
                        android:maxLines="2"
                        android:text="@string/string_option_open_protocol"
                        android:textColor="@color/blue"
                        android:textSize="@dimen/font_13" />

                </LinearLayout>
            </LinearLayout>
        </ScrollView>

        <!-- Ignore CheckBox -->


        <!-- OK&Cancel Button -->

        <View
            android:layout_width="fill_parent"
            android:layout_height="8dip"
            android:background="@color/bgColor_divier" />

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content" >
            <Button
                android:id="@+id/update_id_ok"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/btn_right_selector"
                android:focusable="true"
                android:gravity="center"
                android:padding="12dp"
                android:textColor="#000000"
                android:textSize="16sp" />

            <View
                android:layout_width="0.5dip"
                android:layout_height="fill_parent"
                android:layout_gravity="center_horizontal"
                android:background="#e1dede" />

            <Button
                android:id="@+id/update_id_cancel"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/btn_left_selector"
                android:focusable="true"
                android:gravity="center"
                android:padding="12dp"
                android:textColor="#000000"
                android:textSize="16sp" />
        </LinearLayout>
    </LinearLayout>

</RelativeLayout>