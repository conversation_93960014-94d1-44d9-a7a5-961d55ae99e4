<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dip_46"
    android:background="@color/color_bg_2"
    >

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_1"
        android:layout_alignParentBottom="true"
        android:background="@color/divider_line_color"/>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="@dimen/app_paddingLeft"
        android:paddingRight="@dimen/app_paddingRight">


        <LinearLayout
            android:id="@+id/tab_a_rela"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="@dimen/dip_30"
            android:gravity="center_vertical"
            android:orientation="vertical"
            >


            <TextView
                android:id="@+id/tab_a_name"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_45"
                android:gravity="center_vertical"
                android:text="@string/string_google_auth"
                android:textAppearance="@style/BodyL_Blue_Bold"
                />

            <View
                android:id="@+id/tab_a_indicator"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dip_1"
                android:background="@color/blue" />
        </LinearLayout>


        <LinearLayout
            android:id="@+id/tab_b_rela"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_toRightOf="@+id/tab_a_rela"
            >


            <TextView
                android:id="@+id/tab_b_name"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_45"
                android:gravity="center"
                android:text="@string/string_sms_auth"
                android:textAppearance="@style/BodyL_Dark_Bold"/>

            <View
                android:id="@+id/tab_b_indicator"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dip_1"
                android:visibility="visible"
                android:background="@color/blue" />
        </LinearLayout>
    </RelativeLayout>


</RelativeLayout>