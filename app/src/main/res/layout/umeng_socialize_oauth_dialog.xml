<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="#D4E0E5">


    <RelativeLayout
        android:id="@+id/umeng_socialize_titlebar"
        android:layout_width="match_parent"
        android:layout_height="54dp"
        android:background="#0086DC">

        <TextView
            android:id="@+id/umeng_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text=""
            android:textColor="#ffffff"
            android:textSize="18sp" />

        <RelativeLayout
            android:id="@+id/umeng_back"
            android:layout_width="60dp"
            android:layout_height="match_parent"

            android:onClick="onCancel"
            android:visibility="visible">

            <ImageButton

                android:layout_width="12dp"
                android:layout_height="20dp"
                android:layout_centerVertical="true"
                android:background="@drawable/umeng_socialize_back_icon"
                android:layout_marginLeft="15dp"
                android:scaleType="center" />
        </RelativeLayout>

        <TextView
            android:id="@+id/umeng_share_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerInParent="true"
            android:layout_marginRight="10dp"
            android:background="@drawable/umeng_socialize_btn_bg"
            android:padding="4dp"
            android:text="分享"
            android:textColor="#ffffff" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/umeng_socialize_follow"
        android:layout_width="fill_parent"
        android:layout_height="40dp"
        android:layout_alignParentBottom="true"
        android:background="#AAAAAA"
        android:visibility="visible">

        <CheckBox
            android:id="@+id/umeng_socialize_follow_check"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="20dp"
            android:checked="true"
            android:visibility="gone" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:layout_toRightOf="@id/umeng_socialize_follow_check"
            android:text="关注官方微博"
            android:textColor="#f8f8f8"
            android:textSize="16sp"
            android:visibility="gone" />
    </RelativeLayout>

    <!--<LinearLayout-->
        <!--android:id="@+id/webView_container"-->
        <!--android:layout_width="match_parent"-->
        <!--android:layout_height="match_parent"-->
        <!--android:layout_above="@id/umeng_socialize_follow"-->
        <!--android:layout_below="@id/umeng_socialize_titlebar"-->
        <!--android:orientation="horizontal"-->
        <!--android:visibility="visible" />-->

    <WebView
        android:id="@+id/webView"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:background="#F2F5F6"
        android:layout_above="@id/umeng_socialize_follow"
        android:layout_below="@id/umeng_socialize_titlebar"
        android:visibility="visible" />

    <RelativeLayout
        android:id="@+id/progress_bar_parent"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:layout_below="@id/umeng_socialize_titlebar"
        android:background="#F2F5F6" >

        <LinearLayout
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_centerInParent="true"
            android:orientation="horizontal">

            <ProgressBar
                android:layout_width="fill_parent"
                android:layout_height="fill_parent"
                android:layout_margin="10dp" />
        </LinearLayout>
    </RelativeLayout>

</RelativeLayout>