<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:id="@+id/layout_bottom"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dip_48"
    android:layout_alignParentBottom="true">




    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_48"
        android:layout_toLeftOf="@+id/layout_right"
        android:layout_alignParentStart="true"
        android:layout_marginStart="8dp"
        android:orientation="horizontal">

        <com.bhex.kline.widget.tab.TabTextView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="@string/kline_minutes"
            android:layout_weight="1"
            android:textColor="@color/kline_tab_normal"
            android:textSize="@dimen/kline_tab_textSize"
            android:tag="kind_t_1fenshi"/>

        <com.bhex.kline.widget.tab.TabTextView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="@string/kline_one_minute"
            android:layout_weight="1"
            android:textColor="@color/kline_tab_normal"
            android:textSize="@dimen/kline_tab_textSize"
            android:tag="kind_t_1m"
            />

        <com.bhex.kline.widget.tab.TabTextView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="@string/kline_five_minutes"
            android:layout_weight="1"
            android:textColor="@color/kline_tab_normal"
            android:textSize="@dimen/kline_tab_textSize"
            android:tag="kind_t_5m"
            />

        <com.bhex.kline.widget.tab.TabTextView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="@string/kline_fifteen_minutes"
            android:layout_weight="1"
            android:textColor="@color/kline_tab_normal"
            android:textSize="@dimen/kline_tab_textSize"
            android:tag="kind_t_15m"/>

        <com.bhex.kline.widget.tab.TabTextView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="@string/kline_thirty_minutes"
            android:layout_weight="1"
            android:textColor="@color/kline_tab_normal"
            android:textSize="@dimen/kline_tab_textSize"
            android:tag="kind_t_30m"/>

        <com.bhex.kline.widget.tab.TabTextView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="@string/kline_one_hour"
            android:layout_weight="1"
            android:textColor="@color/kline_tab_normal"
            android:textSize="@dimen/kline_tab_textSize"
            android:tag="kind_t_1h"/>

        <com.bhex.kline.widget.tab.TabTextView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="@string/kline_two_hour"
            android:layout_weight="1"
            android:textColor="@color/kline_tab_normal"
            android:textSize="@dimen/kline_tab_textSize"
            android:tag="kind_t_2h"/>

        <com.bhex.kline.widget.tab.TabTextView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="@string/kline_four_hour"
            android:layout_weight="1"
            android:textColor="@color/kline_tab_normal"
            android:textSize="@dimen/kline_tab_textSize"
            android:tag="kind_t_4h"/>

        <com.bhex.kline.widget.tab.TabTextView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="@string/kline_six_hour"
            android:layout_weight="1"
            android:textColor="@color/kline_tab_normal"
            android:textSize="@dimen/kline_tab_textSize"
            android:tag="kind_t_6h"/>

        <com.bhex.kline.widget.tab.TabTextView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="@string/kline_twelve_hour"
            android:layout_weight="1"
            android:textColor="@color/kline_tab_normal"
            android:textSize="@dimen/kline_tab_textSize"
            android:tag="kind_t_12h"/>

        <com.bhex.kline.widget.tab.TabTextView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="@string/kline_days"
            android:layout_weight="1"
            android:textColor="@color/kline_tab_normal"
            android:textSize="@dimen/kline_tab_textSize"
            android:tag="kind_t_1d"/>

        <com.bhex.kline.widget.tab.TabTextView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="@string/kline_weeks"
            android:layout_weight="1"
            android:textColor="@color/kline_tab_normal"
            android:textSize="@dimen/kline_tab_textSize"
            android:tag="kind_t_1w"/>

        <com.bhex.kline.widget.tab.TabTextView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="@string/kline_months"
            android:layout_weight="1"
            android:textColor="@color/kline_tab_normal"
            android:textSize="@dimen/kline_tab_textSize"
            android:tag="kind_t_1M"/>

    </LinearLayout>
    <LinearLayout
        android:id="@+id/layout_right"
        android:layout_width="@dimen/dip_45"
        android:layout_height="@dimen/dip_48"
        android:layout_alignParentEnd="true"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="3dp"
        android:layout_marginEnd="5dp"
        android:orientation="horizontal"
        android:gravity="center_horizontal"
        >

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tab_land_index"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/index"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:layout_gravity="center"
            android:gravity="center"
            />

        <ImageView
            android:id="@+id/tab_land_index_icon"
            android:layout_width="@dimen/dip_16"
            android:layout_height="@dimen/dip_16"
            android:layout_centerVertical="true"
            android:layout_gravity="center"
            android:layout_marginLeft="@dimen/dip_4"
            android:visibility="gone"
            android:layout_toRightOf="@+id/tab_land_index"
            android:src="@mipmap/icon_arrow_up_black" />

    </LinearLayout>

</RelativeLayout>