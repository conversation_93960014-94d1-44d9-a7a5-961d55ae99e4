<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dip_65"
    android:paddingTop="@dimen/app_padding"
    android:paddingBottom="@dimen/app_padding"
    >

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:orientation="vertical">

        <TextView
            android:id="@+id/item_coinpair"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="ETH/BTC"
            android:textColor="@color/font_color2"
            android:textSize="@dimen/font_15" />

        <TextView
            android:id="@+id/item_amount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_5"
            android:text="24量 1000.5"
            android:textColor="@color/font_color2"
            android:textSize="@dimen/font_12" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:orientation="vertical">

        <TextView
            android:id="@+id/item_price1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="0.0000123"
            android:textColor="@color/font_color2"
            android:textSize="@dimen/font_15" />

        <TextView
            android:id="@+id/item_price2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_5"
            android:text="≈￥12.5"
            android:textColor="@color/font_color2"
            android:textSize="@dimen/font_12" />

    </LinearLayout>

    <TextView
        android:id="@+id/item_range_ratio"
        android:layout_width="@dimen/dip_80"
        android:layout_height="@dimen/dip_30"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:text="+1.6%"
        android:textColor="@color/font_color1"
        android:textSize="@dimen/font_15"
        android:gravity="center"
        android:background="@color/red"
        />

</RelativeLayout>