<?xml version="1.0" encoding="utf-8"?><!--
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: item_otc_list_layout.xml
  ~   @Date: 19-1-11 下午3:03
  ~   @Author: ppzhao
  ~   @Description:
  ~   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/itemView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="@dimen/app_paddingLeft"
        android:paddingTop="@dimen/dip_12"
        android:paddingRight="@dimen/app_paddingRight">

        <TextView
            android:id="@+id/avator"
            android:layout_width="@dimen/dip_24"
            android:layout_height="@dimen/dip_24"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:background="@drawable/otc_avator_bg"
            android:gravity="center"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyS_White_Bold_Default" />

        <TextView
            android:id="@+id/name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginStart="@dimen/dip_8"
            app:layout_constraintBaseline_toBaselineOf="@id/avator"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Dark_Bold"
            app:layout_constraintStart_toEndOf="@+id/avator" />

        <TextView
            android:id="@+id/turnoverRate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyS_Dark"
            />

        <TextView
            android:id="@+id/averagePrice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_8"
            android:text=""
            android:textAppearance="@style/H4_Blue"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/avator" />

        <LinearLayout
            android:id="@+id/pay_way_linear"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_12"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/avator"
            >

            <ImageView
                android:id="@+id/support_pay_wechat"
                android:layout_width="@dimen/dip_14"
                android:layout_height="@dimen/dip_14"
                android:layout_marginTop="@dimen/dip_0"
                android:src="@mipmap/icon_pay_wechat"
                android:visibility="gone" />

            <ImageView
                android:id="@+id/support_pay_alipay"
                android:layout_width="@dimen/dip_14"
                android:layout_height="@dimen/dip_14"
                android:layout_marginTop="@dimen/dip_0"
                android:layout_marginStart="@dimen/dip_20"
                android:src="@mipmap/icon_pay_alipay"
                android:visibility="gone" />

            <ImageView
                android:id="@+id/support_pay_unionpay"
                android:layout_width="@dimen/dip_14"
                android:layout_height="@dimen/dip_14"
                android:layout_marginTop="@dimen/dip_0"
                android:layout_marginStart="@dimen/dip_20"
                android:src="@mipmap/icon_pay_union"
                android:visibility="gone" />

        </LinearLayout>

        <Button
            android:id="@+id/btn_create_otc_order"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dip_32"
            app:layout_constraintTop_toBottomOf="@id/pay_way_linear"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="@dimen/dip_12"
            android:background="@drawable/btn_corner"
            android:paddingLeft="@dimen/dip_12"
            android:paddingRight="@dimen/dip_12"
            android:text=""
            android:textAppearance="@style/BodyS_White_Bold_Default"
            />


        <TextView
            android:id="@+id/title_surplus_quantity"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_8"
            android:text="@string/string_surplus_quantity"
            android:textAppearance="@style/Body_Dark"
            app:layout_constraintTop_toBottomOf="@+id/averagePrice"
            app:layout_constraintStart_toStartOf="parent"
            />

        <TextView
            android:id="@+id/surplus_quantity"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_8"
            android:layout_marginLeft="@dimen/dip_8"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Dark"
            app:layout_constraintStart_toEndOf="@id/title_surplus_quantity"
            app:layout_constraintTop_toBottomOf="@+id/averagePrice" />


        <TextView
            android:id="@+id/title_limit_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_8"
            android:text="@string/string_otc_limit_price"
            android:textAppearance="@style/Body_Dark"
            app:layout_constraintTop_toBottomOf="@+id/title_surplus_quantity"
            app:layout_constraintStart_toStartOf="parent"
            />

        <TextView
            android:id="@+id/limit_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_8"
            android:layout_marginLeft="@dimen/dip_8"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Dark"
            app:layout_constraintStart_toEndOf="@id/title_limit_price"
            app:layout_constraintTop_toBottomOf="@+id/title_surplus_quantity" />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_1"
        android:layout_marginTop="@dimen/dip_12"
        android:background="@color/divider_line_color"
        app:layout_constraintTop_toBottomOf="@id/title_pay_way" />
</LinearLayout>