<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dip_40"
    android:background="@color/white"
    android:paddingLeft="@dimen/app_paddingLeft"
    android:paddingRight="@dimen/app_paddingRight">

    <RelativeLayout
        android:id="@+id/tab_bid_rela"
        android:layout_width="wrap_content"
        android:layout_height="match_parent">


        <TextView
            android:id="@+id/tab_bid"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="@dimen/dip_5"
            android:text="@string/string_purchase"
            android:textColor="@color/blue"
            android:textSize="@dimen/font_15" />

        <View
            android:id="@+id/tab_indicator_bid"
            android:layout_width="@dimen/dip_40"
            android:layout_centerHorizontal="true"
            android:layout_height="@dimen/dip_2"
            android:layout_alignParentBottom="true"
            android:background="@color/blue" />
    </RelativeLayout>


    <RelativeLayout
        android:id="@+id/tab_ask_rela"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_toRightOf="@+id/tab_bid_rela"
        android:layout_marginLeft="@dimen/dip_30"
        >


        <TextView
            android:id="@+id/tab_ask"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:text="@string/string_sellout"
            android:textColor="@color/font_color2"
            android:textSize="@dimen/font_15" />

        <View
            android:id="@+id/tab_indicator_ask"
            android:layout_width="@dimen/dip_40"
            android:layout_height="@dimen/dip_2"
            android:layout_alignParentBottom="true"
            android:visibility="gone"
            android:background="@color/blue" />
    </RelativeLayout>

</RelativeLayout>