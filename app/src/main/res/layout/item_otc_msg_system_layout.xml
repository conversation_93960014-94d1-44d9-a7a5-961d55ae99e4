<?xml version="1.0" encoding="utf-8"?><!--
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: item_otc_msg_system_layout.xml
  ~   @Date: 19-1-30 下午8:00
  ~   @Author: ppzhao
  ~   @Description:
  ~   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  -->

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:padding="@dimen/dip_8">

    <TextView
        android:id="@+id/positionValue"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textAppearance="@style/Caption_Grey"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:text=""
        android:visibility="visible"
        />
    <TextView
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/positionValue"
        android:layout_marginTop="@dimen/dip_8"
        android:padding="@dimen/dip_8"
        android:background="@color/dark5"
        android:textAppearance="@style/Body_Dark"
        android:text=""
        android:layout_marginStart="@dimen/dip_72"
        android:layout_marginEnd="@dimen/dip_72"
        />

    <TextView
        android:id="@+id/uploadProof"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/content"
        android:layout_marginTop="@dimen/dip_8"
        android:padding="@dimen/dip_8"
        android:textAppearance="@style/Body_Blue"
        android:text="@string/string_upload_proof"
        android:layout_marginStart="@dimen/dip_72"
        android:layout_marginEnd="@dimen/dip_72"
        />

</androidx.constraintlayout.widget.ConstraintLayout>