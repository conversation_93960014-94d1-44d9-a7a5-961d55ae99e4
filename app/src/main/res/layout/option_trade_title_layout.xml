<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/titleRela"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    >
    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/divider_line_color"
        android:layout_marginLeft="@dimen/app_margin"
        />
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="@dimen/dip_10"
        android:layout_marginBottom="@dimen/dip_10"
        android:paddingLeft="@dimen/app_margin"
        android:paddingRight="@dimen/app_margin">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_weight="1.1"
            android:orientation="vertical"
            android:layout_height="match_parent"
            android:layout_alignParentLeft="true">

            <TextView
                android:id="@+id/option_exercise_point_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:textAppearance="@style/BodyS_Grey"
                android:singleLine="true"
                android:text="@string/string_exercise_point"
                android:padding="@dimen/dip_2"/>

            <TextView
                android:id="@+id/option_exercise_point"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:padding="@dimen/dip_2"
                android:text="--"
                android:textAppearance="@style/Body_Dark" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_weight="1.2"
            android:orientation="vertical"
            android:layout_height="match_parent"
            android:layout_alignParentLeft="true">

            <TextView
                android:id="@+id/option_exercise_price_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:textAppearance="@style/BodyS_Grey"
                android:singleLine="true"
                android:text="@string/string_exercise_price"
                android:padding="@dimen/dip_2"/>

            <TextView
                android:id="@+id/option_exercise_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:padding="@dimen/dip_2"
                android:text="--"
                android:textAppearance="@style/Body_Dark" />
        </LinearLayout>





        <LinearLayout
            android:layout_width="0dp"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="right"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/option_delivery_time_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:textAppearance="@style/BodyS_Grey"
                android:text="@string/string_exercise_time"
                android:padding="@dimen/dip_2"/>

            <TextView
                android:id="@+id/option_delivery_time"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="right"
                android:layout_centerVertical="true"
                android:padding="@dimen/dip_2"
                android:text="--"
                android:textAppearance="@style/Body_Dark" />
        </LinearLayout>

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_8"
        android:background="@color/divider_line_color"
        />

</LinearLayout>