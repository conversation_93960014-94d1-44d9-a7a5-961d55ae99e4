<?xml version="1.0" encoding="utf-8"?><!--
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: pop_otc_menu_layout.xml
  ~   @Date: 19-1-15 上午11:56
  ~   @Author: ppzhao
  ~   @Description:
  ~   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:minWidth="@dimen/dip_80"
    android:background="@drawable/bg_corner_white"
    android:orientation="vertical"
    >
    
    <TextView
        android:id="@+id/otc_setting"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dip_48"
        android:paddingStart="@dimen/dip_16"
        android:paddingEnd="@dimen/dip_16"
        android:text="@string/string_otc_setting"
        android:gravity="center_vertical"
        android:textAppearance="@style/BodyL_Dark"
        />
    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_1"
        android:background="@color/divider_line_color"
        />
    <TextView
        android:id="@+id/otc_orders"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dip_48"
        android:paddingStart="@dimen/dip_16"
        android:paddingEnd="@dimen/dip_16"
        android:text="@string/string_otc_orders"
        android:gravity="center_vertical"
        android:textAppearance="@style/BodyL_Dark"
        />
    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_1"
        android:background="@color/divider_line_color"
        />
    <TextView
        android:id="@+id/otc_pay_way"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dip_48"
        android:paddingStart="@dimen/dip_16"
        android:paddingEnd="@dimen/dip_16"
        android:text="@string/string_otc_pay_set"
        android:gravity="center_vertical"
        android:textAppearance="@style/BodyL_Dark"
        />

    <View
        android:id="@+id/otc_ad_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_1"
        android:visibility="gone"
        android:background="@color/divider_line_color"
        />
    <TextView
        android:id="@+id/otc_ad_manage"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dip_48"
        android:paddingStart="@dimen/dip_16"
        android:visibility="gone"
        android:paddingEnd="@dimen/dip_16"
        android:text="@string/string_otc_ad_manage"
        android:gravity="center_vertical"
        android:textAppearance="@style/BodyL_Dark"
        />

</LinearLayout>