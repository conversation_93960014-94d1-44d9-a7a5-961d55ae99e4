<?xml version="1.0" encoding="utf-8"?>
<!--
  ~
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: layout_alertview_alert.xml
  ~   @Date: 11/29/18 3:21 PM
  ~   @Author: chenjun
  ~   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  ~
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical" android:layout_width="match_parent"
    android:background="@drawable/bg_alertview_alert"
    android:layout_height="wrap_content">

    <include layout="@layout/include_alertheader"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>
    <ViewStub
        android:id="@+id/viewStubHorizontal"
        android:layout="@layout/layout_alertview_alert_horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />
    <ViewStub
        android:id="@+id/viewStubVertical"
        android:layout="@layout/layout_alertview_alert_vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />
</LinearLayout>