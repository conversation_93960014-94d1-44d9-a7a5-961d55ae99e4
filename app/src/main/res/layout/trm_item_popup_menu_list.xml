<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="45dp"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingTop="12dp"
    android:paddingBottom="12dp" >

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_centerInParent="true"
        android:layout_marginStart="36dp"
        android:layout_marginEnd="36dp"
        >
        <ImageView
            android:id="@+id/trm_menu_item_icon"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_marginRight="8dp"
            android:scaleType="center"
            />
        <TextView
            android:id="@+id/trm_menu_item_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@android:color/black"
            android:textSize="14sp"
            android:gravity="center_horizontal"
            tools:text="累计数量"
            />
    </LinearLayout>
</RelativeLayout>