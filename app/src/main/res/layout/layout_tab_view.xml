<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="56dp"
    xmlns:tools="http://schemas.android.com/tools"
    android:clipToPadding="false"
    android:clipChildren="false">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        android:orientation="vertical">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/tab_animation_view"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginTop="8dp"
            android:src="@mipmap/tab_ic_home"
            />

        <io.bhex.app.bottom.CheckableTextView
            android:id="@+id/tab_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/main_tab_item_ext"
            tools:text="首页"
            android:clickable="false"
            />
    </LinearLayout>

    <!--<androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tab_msg_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginLeft="10dp"
        android:layout_marginBottom="30dp"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="8sp"
        android:visibility="gone" />-->

</FrameLayout>
