<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/color_bg_2"
        android:orientation="vertical">

        <View
            android:id="@+id/myStatusBar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_20"
            android:background="@color/white"
            android:visibility="gone" />

        <io.bhex.app.view.TopBar
            android:id="@+id/topBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:left_text="" />


        <com.scwang.smartrefresh.layout.SmartRefreshLayout
            android:id="@+id/refreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:srlEnableLoadMore="false"
            >

            <io.bhex.app.view.CustomFreshHeader
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:srlClassicsSpinnerStyle="FixedBehind"
                app:srlDrawableArrowSize="20dp"
                app:srlDrawableMarginRight="20dp"
                app:srlDrawableProgressSize="20dp"
                app:srlEnableLastTime="true"
                app:srlFinishDuration="500"
                app:srlTextSizeTime="10dp"
                app:srlTextSizeTitle="16sp"
                app:srlTextTimeMarginTop="2dp" />
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <ProgressBar
                    android:id="@+id/pb"
                    style="@style/Widget.AppCompat.ProgressBar.Horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:indeterminate="false"
                    android:minHeight="2dp"
                    android:progressDrawable="@drawable/web_progress_bar_states" />

                <WebView
                    android:id="@+id/webView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="visible"/>

                <RelativeLayout
                    android:id="@+id/ll_network_error"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="top|center_horizontal"
                    android:orientation="vertical"
                    android:visibility="gone">


                    <TextView
                        android:id="@+id/tv_error_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:text="@string/string_net_error"
                        android:textAppearance="@style/BodyL_Dark" />
                </RelativeLayout>
            </LinearLayout>

            <com.scwang.smartrefresh.layout.footer.ClassicsFooter
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />
        </com.scwang.smartrefresh.layout.SmartRefreshLayout>
    </LinearLayout>

</RelativeLayout>