<?xml version="1.0" encoding="utf-8"?>
<!--
  ~
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: layout_alertview_actionsheet.xml
  ~   @Date: 11/29/18 3:21 PM
  ~   @Author: chenjun
  ~   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  ~
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/loAlertHeader"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1.0"
        android:background="@drawable/bg_actionsheet_header"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvAlertTitle"
            android:layout_width="match_parent"
            android:layout_height="@dimen/height_actionsheet_title"
            android:gravity="center"
            android:textColor="@color/textColor_actionsheet_title"
            android:textSize="@dimen/textSize_actionsheet_title" />
        <View
            android:id="@+id/divider_title"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:background="#0c242b32"/>

        <TextView
            android:id="@+id/tvAlertMsg"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/marginBottom_actionsheet_msg"
            android:gravity="center"
            android:textColor="@color/textColor_actionsheet_msg"
            android:textSize="@dimen/textSize_actionsheet_msg" />

        <ListView
            android:id="@+id/alertButtonListView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1.0"
            android:cacheColorHint="@android:color/transparent"
            android:divider="@null"
            android:scrollbars="none" />
    </LinearLayout>

    <TextView
        android:id="@+id/tvAlertCancel"
        android:layout_width="match_parent"
        android:layout_height="@dimen/height_alert_button"
        android:layout_marginTop="@dimen/margin_actionsheet_left_right"
        android:background="@drawable/bg_actionsheet_cancel"
        android:clickable="true"
        android:gravity="center"
        android:textColor="@color/textColor_alert_button_cancel"
        android:textSize="@dimen/textSize_alert_button"
        android:textStyle="bold"
        android:visibility="gone" />
</LinearLayout>