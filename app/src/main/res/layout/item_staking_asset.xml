<?xml version="1.0" encoding="utf-8"?><!--
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: item_finance_list_item_layout.xml
  ~   @Date: 19-3-8 下午2:50
  ~   @Author: ppzhao
  ~   @Description:
  ~   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  -->

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/itemView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="@dimen/dip_15"
    android:paddingLeft="@dimen/dip_16"
    android:paddingRight="@dimen/dip_16">

    <TextView
        android:id="@+id/finance_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/string_placeholder"
        android:textColor="@color/dark"
        android:textSize="@dimen/sp_15"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dip_24"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:paddingRight="@dimen/dip_4"
        android:background="@drawable/bg_staking_order_status"
        android:paddingLeft="@dimen/dip_4"
        android:gravity="center">

        <ImageView
            android:id="@+id/iv_status"
            android:layout_width="@dimen/dip_16"
            android:layout_height="@dimen/dip_16"
            android:src="@mipmap/icon_staking_order_subscribing">

        </ImageView>

        <TextView
            android:id="@+id/tv_status"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dip_24"
            android:layout_marginLeft="@dimen/dip_2"
            android:text="@string/string_placeholder"
            android:textColor="@color/dark"
            android:textSize="@dimen/sp_12"
            android:gravity="center"/>
    </LinearLayout>

    <TextView
        android:id="@+id/tv_hold_amount_title"
        android:layout_width="@dimen/dip_0"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_12"
        android:singleLine="true"
        android:text="@string/string_staking_hold_amount"
        android:textAppearance="@style/BodyS_Grey"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/finance_title"
        app:layout_constraintWidth_percent="0.34" />

    <TextView
        android:id="@+id/tv_days_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_12"
        android:singleLine="true"
        android:text="@string/string_product_days1"
        android:textAppearance="@style/BodyS_Grey"
        android:textSize="@dimen/sp_12"
        app:layout_constraintStart_toEndOf="@id/tv_hold_amount_title"
        app:layout_constraintTop_toBottomOf="@id/finance_title"
        app:layout_constraintWidth_percent="0.33" />


    <TextView
        android:id="@+id/tv_reference_apr_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dip_4"
        android:layout_marginTop="@dimen/dip_12"
        android:singleLine="true"
        android:text="@string/string_reference_apr"
        android:textAppearance="@style/BodyS_Grey"
        android:textSize="@dimen/sp_12"
        android:gravity="right"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/finance_title"
        app:layout_constraintWidth_percent="0.33" />

    <TextView
        android:id="@+id/tv_hold_amount_value"
        android:layout_width="@dimen/dip_0"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_4"
        android:singleLine="true"
        android:textColor="@color/dark"
        android:textSize="@dimen/sp_14"
        android:textStyle="bold"
        android:gravity="center_vertical"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_hold_amount_title"
        app:layout_constraintWidth_percent="0.34" />

    <TextView
        android:id="@+id/tv_days_value"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_4"
        android:singleLine="true"
        android:textColor="@color/dark"
        android:textSize="@dimen/sp_14"
        android:textStyle="bold"
        android:gravity="center_vertical"
        app:layout_constraintStart_toEndOf="@id/tv_hold_amount_title"
        app:layout_constraintTop_toBottomOf="@id/tv_hold_amount_title"
        app:layout_constraintWidth_percent="0.33" />


    <TextView
        android:id="@+id/tv_reference_apr_value"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dip_4"
        android:layout_marginTop="@dimen/dip_4"
        android:singleLine="true"
        android:textColor="@color/dark"
        android:textSize="@dimen/sp_14"
        android:textStyle="bold"
        android:gravity="right|center_vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_hold_amount_title"
        app:layout_constraintWidth_percent="0.33" />

    <TextView
        android:id="@+id/tv_end_date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_12"
        android:singleLine="true"
        android:text="@string/string_end_date"
        android:textAppearance="@style/BodyS_Grey"
        android:textSize="@dimen/sp_12"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_hold_amount_value"/>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/app_line"
        app:layout_constraintTop_toBottomOf="@id/tv_end_date"
        android:layout_marginTop="@dimen/dip_12"
        android:background="@color/divider_line_color" />
</androidx.constraintlayout.widget.ConstraintLayout>