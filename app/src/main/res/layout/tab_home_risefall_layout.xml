<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:layout_width="wrap_content"
    android:layout_height="48dp"
    android:layout_gravity="center"
    android:gravity="center"
    >
    <ImageView
        android:layout_gravity="center"
        android:id="@+id/tabicon"
        android:src="@mipmap/ic_launcher"
        android:layout_width="@dimen/dip_25"
        android:layout_height="@dimen/dip_25" />
    <TextView
        android:id="@+id/tabtext"
        android:gravity="center"
        android:textSize="@dimen/font_15"
        android:text=""
        android:textColor="@color/tab_text_color_night"
        android:layout_marginLeft="@dimen/dip_3"
        android:layout_width="wrap_content"
        android:layout_height="match_parent" />
</LinearLayout>