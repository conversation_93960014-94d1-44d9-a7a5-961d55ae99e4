<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/shareView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_margin="@dimen/dip_0"
    android:background="@drawable/bg_share_profit">

<!--    <LinearLayout-->
<!--        android:id="@+id/contentLinear"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layout_alignParentBottom="true"-->
<!--        android:background="@mipmap/share_box"-->
<!--        android:layout_margin="@dimen/dip_4"-->
<!--        android:gravity="bottom"-->
<!--        android:orientation="vertical">-->

    <LinearLayout
        android:id="@+id/contentLinear"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@drawable/bg_card_white"
        android:gravity="bottom"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/shareContent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_above="@id/qrcode_area"
            android:visibility="visible"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <TextView
                android:id="@+id/share_emoji_description"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dip_12"
                android:layout_marginTop="@dimen/dip_16"
                android:layout_marginRight="@dimen/dip_12"
                android:layout_gravity="center_horizontal"
                android:gravity="center"
                android:text=""
                android:maxLines="2"
                android:ellipsize="end"
                android:textAppearance="@style/H4_Dark" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dip_1"
                android:layout_marginLeft="@dimen/dip_12"
                android:layout_marginTop="@dimen/dip_16"
                android:layout_marginRight="@dimen/dip_12"
                android:background="@color/divider_line_color" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dip_16"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/share_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/string_profit_rate"
                    android:textAppearance="@style/Body_Dark" />

                <TextView
                    android:id="@+id/token_trade_type"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dip_8"
                    android:background="@drawable/bg_corner_red"
                    android:padding="@dimen/dip_2"
                    android:text="@string/string_placeholder"
                    android:textAppearance="@style/Caption_White_Default" />

                <TextView
                    android:id="@+id/token_rise_or_fall"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dip_8"
                    android:background="@drawable/bg_corner_green"
                    android:padding="@dimen/dip_2"
                    android:text="@string/string_placeholder"
                    android:textAppearance="@style/Caption_White_Default" />


            </LinearLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dip_8"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/profit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/string_placeholder"
                    android:textAppearance="@style/H3_Green" />
                <TextView
                    android:id="@+id/unit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toRightOf="@id/profit"
                    android:layout_marginLeft="@dimen/dip_4"
                    android:layout_alignBaseline="@id/profit"
                    android:text="@string/string_placeholder"
                    android:textAppearance="@style/Body_Green_Bold" />
            </RelativeLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/dip_60"
                android:layout_marginStart="@dimen/dip_12"
                android:layout_marginBottom="@dimen/dip_24"
                android:layout_marginTop="@dimen/dip_8"
                android:layout_marginEnd="@dimen/dip_12">

                <LinearLayout
                    android:id="@+id/content1"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintWidth_percent="0.36"
                    android:layout_width="@dimen/dip_0"
                    android:layout_height="match_parent"
                    android:gravity="left|center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/price_title1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:singleLine="true"
                        android:text="@string/string_placeholder"
                        android:textAppearance="@style/BodyS_Grey" />

                    <TextView
                        android:id="@+id/price1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dip_4"
                        android:singleLine="false"
                        android:text="@string/string_placeholder"
                        android:textAppearance="@style/BodyS_Dark" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/content2"
                    app:layout_constraintStart_toEndOf="@id/content1"
                    app:layout_constraintWidth_percent="0.32"
                    android:layout_width="@dimen/dip_0"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/price_title2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:singleLine="false"
                        android:text="@string/string_placeholder"
                        android:textAppearance="@style/BodyS_Grey" />

                    <TextView
                        android:id="@+id/price2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dip_4"
                        android:singleLine="true"
                        android:text="@string/string_placeholder"
                        android:textAppearance="@style/BodyS_Dark" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/content3"
                    app:layout_constraintStart_toEndOf="@id/content2"
                    app:layout_constraintWidth_percent="0.32"
                    android:layout_width="@dimen/dip_0"
                    android:layout_height="match_parent"
                    android:gravity="right|center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/price_title3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:singleLine="false"
                        android:text="@string/string_placeholder"
                        android:textAppearance="@style/BodyS_Grey" />

                    <TextView
                        android:id="@+id/price3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dip_4"
                        android:singleLine="true"
                        android:text="@string/string_placeholder"
                        android:textAppearance="@style/BodyS_Dark" />

                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>
        </LinearLayout>

        <RelativeLayout
            android:id="@+id/qrcode_area"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_80"
            android:visibility="visible"
            android:background="@color/blue10">

            <ImageView
                android:id="@+id/logo"
                android:layout_width="@dimen/dip_50"
                android:layout_height="@dimen/dip_50"
                android:layout_centerVertical="true"
                android:layout_marginLeft="@dimen/dip_12"
                android:src="@mipmap/ic_launcher" />

            <ImageView
                android:id="@+id/qrcode"
                android:layout_width="@dimen/dip_50"
                android:layout_height="@dimen/dip_50"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="@dimen/dip_12" />

            <TextView
                android:id="@+id/name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignTop="@id/logo"
                android:layout_marginLeft="@dimen/dip_8"
                android:layout_marginRight="@dimen/dip_8"
                android:layout_toLeftOf="@id/qrcode"
                android:layout_toRightOf="@id/logo"
                android:ellipsize="end"
                android:singleLine="true"
                android:text="@string/app_name"
                android:textAppearance="@style/H4_Dark" />

            <TextView
                android:id="@+id/desp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/name"
                android:layout_marginLeft="@dimen/dip_8"
                android:layout_marginRight="@dimen/dip_8"
                android:layout_toLeftOf="@id/qrcode"
                android:layout_toRightOf="@id/logo"
                android:ellipsize="end"
                android:singleLine="true"
                android:text="@string/share_exchange_desp"
                android:textAppearance="@style/Body_Dark" />

        </RelativeLayout>

    </LinearLayout>

    <ImageView
        android:id="@+id/share_emoji"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignParentTop="true"
        android:layout_above="@id/contentLinear"
        android:layout_centerHorizontal="true"
        android:src="@mipmap/profit_rise" />

</RelativeLayout>