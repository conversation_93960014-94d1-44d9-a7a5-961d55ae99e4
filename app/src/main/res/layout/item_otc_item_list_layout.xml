<?xml version="1.0" encoding="utf-8"?><!--
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: item_otc_item_list_layout.xml
  ~   @Date: 19-3-4 下午2:50
  ~   @Author: ppzhao
  ~   @Description:
  ~   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  -->

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/item_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="40dp"
        android:paddingTop="12dp"
        android:paddingEnd="40dp"
        android:paddingBottom="12dp"
        android:gravity="center"
        android:text="@string/string_placeholder"
        android:textAppearance="@style/Body_Dark"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_1"
        android:minWidth="@dimen/dip_60"
        android:background="@color/divider_line_color"
        app:layout_constraintTop_toBottomOf="@id/item_name"
        />

</androidx.constraintlayout.widget.ConstraintLayout>