<?xml version="1.0" encoding="utf-8"?><!--
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: item_otc_receipt_channel_aliorwechat_layout.xml
  ~   @Date: 19-2-1 上午11:19
  ~   @Author: ppzhao
  ~   @Description:
  ~   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  -->

<cn.bingoogolapple.swipeitemlayout.BGASwipeItemLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/item_root"
        android:layout_marginLeft="2dp"
        app:bga_sil_bottomMode="pullOut"
        app:bga_sil_springDistance="20dp"
        app:bga_sil_swipeDirection="left">
    <!-- BGASwipeItemLayout 的 layout_marginLeft 设置成了 2dp，以便触发滑动返回 -->

    <TextView
        android:id="@+id/item_delete"
        android:layout_height="match_parent"
        android:layout_width="wrap_content"
        android:layout_margin="@dimen/dip_16"
        android:background="@drawable/selector_btn_red"
        android:clickable="true"
        android:gravity="center"
        android:paddingLeft="20dp"
        android:paddingRight="20dp"
        android:text="删除"
        android:textAppearance="@style/H4_White"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/itemView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/app_margin_left"
        android:layout_marginTop="@dimen/dip_4"
        android:layout_marginEnd="@dimen/app_margin_right"
        android:layout_marginBottom="@dimen/dip_8"
        android:background="@drawable/bg_corner_white"
        android:paddingStart="@dimen/app_paddingLeft"
        android:paddingTop="@dimen/dip_8"
        android:paddingEnd="@dimen/dip_8"
        android:paddingBottom="@dimen/app_paddingBottom"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/icon"
            android:layout_width="@dimen/dip_20"
            android:layout_height="@dimen/dip_20"
            android:layout_marginTop="@dimen/dip_12"
            android:src="@mipmap/icon_pay_alipay"
            android:textAppearance="@style/H3_Dark"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/toggleButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@mipmap/icon_switch_button_off"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />



        <TextView
            android:id="@+id/title_account"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_16"
            android:text="@string/string_otc_account"
            android:textAppearance="@style/Body_Grey"
            app:layout_constraintTop_toBottomOf="@+id/icon"
            app:layout_constraintWidth_percent="0.4" />

        <TextView
            android:id="@+id/account"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dip_12"
            android:layout_marginTop="@dimen/dip_16"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Dark"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/title_account"
            app:layout_constraintTop_toBottomOf="@+id/icon" />

        <TextView
            android:id="@+id/title_name"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_16"
            android:text="@string/string_name"
            android:textAppearance="@style/Body_Grey"
            app:layout_constraintTop_toBottomOf="@+id/account"
            app:layout_constraintWidth_percent="0.4" />

        <TextView
            android:id="@+id/name"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dip_12"
            android:layout_marginTop="@dimen/dip_16"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Dark"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/title_name"
            app:layout_constraintTop_toBottomOf="@+id/account" />
        <TextView
            android:id="@+id/title_qrcode"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_16"
            android:text="@string/string_qr_code"
            android:textAppearance="@style/Body_Grey"
            app:layout_constraintTop_toBottomOf="@+id/name"
            app:layout_constraintWidth_percent="0.4" />

        <ImageView
            android:id="@+id/qrcode"
            android:layout_width="@dimen/dip_24"
            android:layout_height="@dimen/dip_24"
            android:src="@mipmap/icon_qcode"
            android:layout_marginStart="@dimen/dip_12"
            android:layout_marginTop="@dimen/dip_16"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Dark"
            app:layout_constraintStart_toEndOf="@id/title_qrcode"
            app:layout_constraintTop_toBottomOf="@+id/name" />


    </androidx.constraintlayout.widget.ConstraintLayout>

</cn.bingoogolapple.swipeitemlayout.BGASwipeItemLayout>