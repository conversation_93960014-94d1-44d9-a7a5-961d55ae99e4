<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white">
    <!--android:background="@drawable/bg_corner_rect_gray">-->

    <RelativeLayout
        android:id="@+id/editRela"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_43">

        <LinearLayout
            android:id="@+id/btn_plus_sign"
            android:layout_width="@dimen/dip_30"
            android:layout_height="@dimen/dip_40"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@mipmap/icon_plus" />
        </LinearLayout>

        <View
            android:id="@+id/app_line1"
            android:layout_width="@dimen/dip_0.25"
            android:layout_height="@dimen/dip_15"
            android:layout_centerVertical="true"
            android:layout_toLeftOf="@id/btn_plus_sign"
            android:background="@color/divider_line_color" />

        <LinearLayout
            android:id="@+id/btn_minus_sign"
            android:layout_width="@dimen/dip_30"
            android:layout_height="@dimen/dip_40"
            android:layout_centerVertical="true"
            android:layout_toLeftOf="@id/app_line1"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@mipmap/icon_minus_sign" />
        </LinearLayout>

        <View
            android:id="@+id/app_line2"
            android:layout_width="@dimen/dip_0.5"
            android:layout_height="match_parent"
            android:layout_marginBottom="@dimen/dip_2"
            android:layout_centerVertical="true"
            android:layout_toLeftOf="@id/btn_minus_sign"
            android:background="@color/divider_line_color" />

        <EditText
            android:id="@+id/editView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_toLeftOf="@id/app_line2"
            android:background="@null"
            android:inputType="numberDecimal"
            android:maxLines="1"
            android:paddingLeft="@dimen/dip_8"
            android:paddingRight="@dimen/dip_3"
            android:text=""
            />

    </RelativeLayout>
</RelativeLayout>