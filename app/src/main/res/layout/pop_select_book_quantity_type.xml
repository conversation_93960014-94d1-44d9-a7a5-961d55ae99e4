<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:id="@+id/cumulateQuantity"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_45"
            android:gravity="center"
            android:paddingLeft="@dimen/app_paddingLeft"
            android:paddingRight="@dimen/app_paddingRight"
            android:text="@string/string_cumulative_quantity_format"
            android:textAppearance="@style/Body_Dark" />

        <TextView
            android:id="@+id/quantity"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_45"
            android:gravity="center"
            android:paddingLeft="@dimen/app_paddingLeft"
            android:paddingRight="@dimen/app_paddingRight"
            android:text="@string/string_amount"
            android:textAppearance="@style/Body_Dark" />
    </LinearLayout>
</LinearLayout>