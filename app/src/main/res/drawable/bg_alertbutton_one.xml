<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: bg_alertbutton_none.xml
  ~   @Date: 11/29/18 3:21 PM
  ~   @Author: chenjun
  ~   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  ~
  -->

<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!--点击效果-->
    <item android:state_pressed="true">
        <shape xmlns:android="http://schemas.android.com/apk/res/android" android:shape="rectangle">
            <!-- android:radius 弧形的半径 -->
            <solid android:color="@color/bgColor_alert_button_press"/>
            <corners android:radius="@dimen/radius_alertview"/>
        </shape>
    </item>
    <item android:drawable="@android:color/transparent" />
</selector>