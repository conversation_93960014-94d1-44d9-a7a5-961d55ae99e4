package io.bhex.sdk.account;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;

import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.core.AsyncTask;
import io.bhex.baselib.core.SPEx;
import io.bhex.baselib.network.Utils.Convert;
import io.bhex.baselib.network.Utils.CookieUtils;
import io.bhex.baselib.utils.DebugLog;
import io.bhex.sdk.BhexSdk;
import io.bhex.sdk.account.bean.UserInfoBean;
import io.bhex.sdk.data_manager.NetWorkApiManager;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2020-07-09
 * 邮   箱：
 * 描   述：
 * ================================================
 */

public class UserManager {
    private volatile static UserManager ourInstance;
    private volatile UserInfoBean userInfo;
    private volatile boolean fingerOpen;
    private volatile boolean fingerAuth;

    public static UserManager getInstance() {
        if (ourInstance == null) {
            synchronized (UserManager.class){
                if (ourInstance == null) {
                    ourInstance = new UserManager();
                }
            }
        }
        return ourInstance;
    }

    private UserManager() {
        loadUserInfoFromCacelFile();
    }

    /**
     * 加载用户信息从缓存文件
     */
    // 修改为同步加载
    private synchronized void loadUserInfoFromCacelFile() {
        fingerOpen = SPEx.isFingerOpen();
        fingerAuth = SPEx.isFingerAuth();
        String userStr = SPEx.get(AppData.SPKEY.USER_INFO_KEY,"");
        if (!TextUtils.isEmpty(userStr)){
            userInfo = Convert.fromJson(userStr, UserInfoBean.class);
        }
    }


    public synchronized void saveUserInfo(UserInfoBean data){
        userInfo = data;

        saveUserInfoInfoFile(data);

    }

    /**
     * 保存用户信息到缓存文件
     * @param data
     */
    private void saveUserInfoInfoFile(UserInfoBean data) {
        if (data == null) {
            DebugLog.w("user info is null");
            return;
        }

        new AsyncTask<UserInfoBean,Integer, Boolean>(){

            @Override
            protected Boolean doInBackground(UserInfoBean... userInfoBeans) {
                UserInfoBean data = userInfoBeans[0];
                SPEx.set(AppData.SPKEY.USER_INFO_KEY, Convert.toJson(data));
//                SPEx.set(AppData.SPKEY.USER_INFO_ID, data.getUserId());
//                SPEx.set(AppData.SPKEY.USER_INFO_EMAIL, data.getEmail());
//                SPEx.set(AppData.SPKEY.USER_INFO_MOBILE, data.getMobile());
//                SPEx.set(AppData.SPKEY.USER_INFO_BINDGA, data.isBindGA());
//                SPEx.set(AppData.SPKEY.USER_INFO_ACCOUNT_ID, data.getDefaultAccountId());
                return true;
            }
        }.execute(data);
    }

    /**
     * 获取用户信息
     * @return
     */
    public synchronized UserInfoBean getUserInfo(){
        return userInfo;
    }

    //清除用户信息
    public void clearUserInfo(){
        userInfo = null;
//        CookieUtils.clearCookies(BhexSdk.getContext());
        SPEx.remove(AppData.SPKEY.USER_INFO_KEY);
//        SPEx.remove(AppData.SPKEY.FINGER_PWD_KEY);
//        SPEx.remove(AppData.SPKEY.GESTURE_PWD_KEY);
        updateFingerAuthStatus(false);//认证状态 false
        NetWorkApiManager.releaseTradeInstance();
    }

    /**
     * 获取默认的UserId
     */
    public String getUserId(){
        if (userInfo != null) {
            return userInfo.getUserId();
        }else{
            return "";
        }
    }

    /**
     * 获取默认的AccountId
     */
    public String getDefaultAccountId(){
        if (userInfo != null) {
            return userInfo.getDefaultAccountId();
        }else{
            return "";
        }
    }

    /**
     * 是否登录
     * @return
     */
    public boolean isLogin(){
        if (userInfo == null) {
            return false;
        }
        String cookieToken = CookieUtils.getInstance().getCookieToken();
        if (TextUtils.isEmpty(cookieToken)) {
            return false;
        }
        if (fingerOpen) {
            return !TextUtils.isEmpty(userInfo.getUserId())&&(fingerAuth);
        }else{
            return !TextUtils.isEmpty(userInfo.getUserId());
        }
    }

    /**
     * 如果登录的话返回true，如果未登录去登录，登录成功之后回调，
     * 该方法考虑在已登录调用方法和登录成功回调不一致使用，如果行为一致使用LoginAndGoin(Context context, LoginResultCallback callback)
     * @param context
     * @param callback
     * @return
     */
    public boolean isLogin(Context context, LoginResultCallback callback){
        if (userInfo == null) {
            BhexSdk.NoCookieNotity(context,new LoginResultCarrier(callback));
            return false;
        }
        String cookieToken = CookieUtils.getInstance().getCookieToken();
        if (TextUtils.isEmpty(cookieToken)) {
            BhexSdk.NoCookieNotity(context,new LoginResultCarrier(callback));
            return false;
        }

        if (fingerOpen) {
            if(!TextUtils.isEmpty(userInfo.getUserId())&&(fingerAuth)){
                return true;
            }
            BhexSdk.NoCookieNotity(context,new LoginResultCarrier(callback));
            return false;
        }else{
            if (TextUtils.isEmpty(userInfo.getUserId())) {
                //IntentUtils.goLogin(context,caller);
                BhexSdk.NoCookieNotity(context,new LoginResultCarrier(callback));
                return false;
            }
            return true;
        }
    }

    /**
     * 如果登录的话处理回调，如果未登录去登录，登录成功之后回调，
     * @param context
     * @param callback
     */
    public void LoginAndGoin(Context context, LoginResultCallback callback){
        if (userInfo == null) {
            BhexSdk.NoCookieNotity(context,new LoginResultCarrier(callback));
            return;
        }
        String cookieToken = CookieUtils.getInstance().getCookieToken();
        if (TextUtils.isEmpty(cookieToken)) {
            BhexSdk.NoCookieNotity(context,new LoginResultCarrier(callback));
            return;
        }

        if (fingerOpen) {
            if(!TextUtils.isEmpty(userInfo.getUserId())&&(fingerAuth)){
                if (callback!=null) {
                    callback.onLoginSucceed();
                }
                return;
            }
            BhexSdk.NoCookieNotity(context,new LoginResultCarrier(callback));
            return;
        }else{
            if (TextUtils.isEmpty(userInfo.getUserId())) {
                //IntentUtils.goLogin(context,caller);
                BhexSdk.NoCookieNotity(context,new LoginResultCarrier(callback));
                return;
            }
            if (callback!=null) {
                callback.onLoginSucceed();
            }
            return;
        }
    }

    public void updateFingerAuthStatus(boolean isAuth) {
        fingerAuth = isAuth;
        SPEx.setFingerAuth(isAuth);
    }

    public boolean isFingerAuthStatus() {
        return fingerAuth;
    }

    public void updateFingerSetOpenStatus(boolean isSetOpen) {
        fingerOpen = isSetOpen;
        SPEx.setFingerOpen(isSetOpen);
    }

    public boolean isFingerSetOpenStatus() {
        return fingerOpen;
    }
}
