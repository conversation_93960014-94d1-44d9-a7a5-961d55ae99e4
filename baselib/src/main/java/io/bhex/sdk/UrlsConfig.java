/*
 *
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: UrlsConfig.java
 *   @Date: 11/29/18 3:21 PM
 *   @Author: ch<PERSON>jun
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 *
 */

package io.bhex.sdk;

import android.text.TextUtils;

import io.bhex.baselib.utils.DebugLog;

public class UrlsConfig {

    public static final String UPLOADREQUEST_URL = "https://analyze.bhfastime.com/mobile";

    // 静态域名配置 - 直接在这里修改域名
    private static final String DOMAIN = "*************";
    
    // 直接初始化URL配置
    public static final String API_SERVER_URL = isIPAddress(DOMAIN) ? "http://" + DOMAIN + "/" : "https://app." + DOMAIN + "/";
    public static final String API_SOCKET_URL = isIPAddress(DOMAIN) ? "ws://" + DOMAIN + "/" : "wss://ws." + DOMAIN + "/";
    public static final String API_H5_URL = isIPAddress(DOMAIN) ? "http://" + DOMAIN + "/" : "https://www." + DOMAIN + "/";
    public static final String API_OTC_URL = isIPAddress(DOMAIN) ? "http://" + DOMAIN + "/" : "https://otc." + DOMAIN + "/";

    public static void init(final boolean bhex, Config config){
        // 直接初始化所有URL，不需要复杂的域名切换逻辑
        Urls.initBrokerUrls();
        OTCUrls.initOTCUrls();
        DebugLog.e("DOMAIN-CONFIG","域名配置完成: " + DOMAIN);
        DebugLog.e("DOMAIN-CONFIG","API_SERVER_URL: " + API_SERVER_URL);
        DebugLog.e("DOMAIN-CONFIG","API_SOCKET_URL: " + API_SOCKET_URL);
    }

    /**
     * 判断字符串是否为IP地址格式
     * @param domain 域名或IP地址
     * @return true如果是IP地址格式，false如果是域名格式
     */
    private static boolean isIPAddress(String domain) {
        if (TextUtils.isEmpty(domain)) {
            return false;
        }
        
        // 简单的IP地址正则匹配
        String ipPattern = "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$";
        return domain.matches(ipPattern);
    }

}