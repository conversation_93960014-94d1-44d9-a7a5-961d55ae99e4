/*
 *
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: AppConfigManager.java
 *   @Date: 11/29/18 3:21 PM
 *   @Author: chenjun
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 *
 */

package io.bhex.sdk.data_manager;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;
import com.tencent.mmkv.MMKV;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;

import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.core.SPEx;
import io.bhex.baselib.network.BParamsBuilder;
import io.bhex.baselib.network.HttpUtils;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.Utils.Convert;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.params.GetParams;
import io.bhex.baselib.network.params.PostParams;
import io.bhex.baselib.network.response.ResponseListener;
import io.bhex.baselib.network.response.ResultResponse;
import io.bhex.baselib.utils.DebugLog;
import io.bhex.baselib.utils.DevicesUtil;
import io.bhex.baselib.utils.JsonConvertor;
import io.bhex.baselib.utils.NumberUtils;
import io.bhex.baselib.utils.SP;
import io.bhex.baselib.utils.SignUtils;
import io.bhex.sdk.BhexSdk;
import io.bhex.sdk.Config;
import io.bhex.sdk.Urls;
import io.bhex.sdk.account.AccountInfoApi;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.config.BackupDomainResponse;
import io.bhex.sdk.config.BasicFunctionsManager;
import io.bhex.sdk.config.ConfigApi;
import io.bhex.sdk.config.bean.BackupDomainList;
import io.bhex.sdk.config.bean.FunctionsBean;
import io.bhex.sdk.config.domain.BackupDomainManager;
import io.bhex.sdk.enums.COIN_TYPE;
import io.bhex.sdk.quote.FavoriteChangeEvent;
import io.bhex.sdk.quote.bean.ChainType;
import io.bhex.sdk.quote.bean.CoinPairBean;
import io.bhex.sdk.quote.bean.EtfPriceBean;
import io.bhex.sdk.quote.bean.FavoriteBean;
import io.bhex.sdk.quote.bean.FavoriteListResponse;
import io.bhex.sdk.quote.bean.FavoriteRecordBean;
import io.bhex.sdk.quote.bean.NewCoinPairListBean;
import io.bhex.sdk.quote.bean.OptionCategoryBean;
import io.bhex.sdk.quote.bean.OtcConfigBean;
import io.bhex.sdk.quote.bean.QuoteTokensBean;
import io.bhex.sdk.trade.margin.MarginApi;
import io.bhex.sdk.trade.margin.bean.MarginTokenConfigResponse;

public class AppConfigManager {
    private volatile static AppConfigManager mInstance;
    private MMKV mmkvSymbols;
    private NewCoinPairListBean mNewCoinPairListBean;
    private Handler mHandler;
    private HashMap<String, CoinPairBean> symbolMap = new HashMap<>();
    private HashMap<String, QuoteTokensBean.TokenItem> tokenMap = new HashMap<>();
    private HashMap<String, MarginTokenConfigResponse.MarginToken> marginTokenMap = new HashMap<>();
    private HashMap<String, Integer> symbolDigitMap = new HashMap<>();
    private HashMap<String, CoinPairBean> optionSymbolMap = new HashMap<>();
    private HashMap<String, Integer> optionSymbolDigitMap = new HashMap<>();
    private HashMap<String, CoinPairBean> futuresSymbolMap = new HashMap<>();
    private HashMap<String, Integer> futuresSymbolDigitMap = new HashMap<>();
    private HashMap<String, OptionCategoryBean> optionCategoryMap = new HashMap<>();
    private List<LoginstatusChange> mObserverList = new ArrayList<>();
    private List<OptionSymbolsChange> mOptionSymbolsChangeList = new ArrayList<>();
    private List<FuturesSymbolsChange> mFuturesSymbolsChangeList = new ArrayList<>();
    private LinkedHashMap<String, FavoriteBean> favoritesMap = new LinkedHashMap<>(); //自选列表 TODO 注意：Map 的 key = exchangeId + symbolId
    private HashMap<String, CoinPairBean> mMarginSymbolMap = new HashMap<>();
    private HashMap<String, Integer> marginSymbolDigitMap = new HashMap<>();


    public static final String PREFERENCE_NEW_SYMBOLS = "newSymbols";
    public static final String PREFERENCE_MARGIN_TOKENS = "margin_tokens";
    private boolean bCacheNewSymbols = true;
    private Timer timer;
    private TimerTask task;
    private static final int nPeriod = 60 * 1000 * 3;   //3分钟
    private static final int NEWSYMBOLS_MESSAGE = 0X3;
    private static final int REQUEST_BACKUP_DOMAIN = 0X4;

    private CoinPairBean mDefaultBBTradeCoinPair;
    private CoinPairBean mDefaultMarginTradeCoinPair;
    private CoinPairBean mDefaultOptionTradeCoinPair;
    private CoinPairBean mDefaultFuturesTradeCoinPair;
    private String orgId = "";
    private String realtimeInterval = "";
    private String checkSum = "";

    public interface LoginstatusChange {
        void onFavriateChange();

    }

    public interface OptionSymbolsChange {
        void onOptionSymbolsChange();

    }

    public interface FuturesSymbolsChange {
        void onFuturesSymbolsChange();

    }

    //获取AppConfigManager单例实例
    public static AppConfigManager GetInstance() {
        if (mInstance == null) {
            synchronized (AppConfigManager.class) {
                if (mInstance == null) {
                    mInstance = new AppConfigManager();
                }
            }
        }

        return mInstance;
    }

    //私有构造函数，初始化配置管理器
    private AppConfigManager() {
        mmkvSymbols = MMKV.mmkvWithID("APP_SYMBOLS");
        mHandler = new Handler(Looper.getMainLooper()) {
            @Override
            public void handleMessage(Message msg) {
                super.handleMessage(msg);
                switch (msg.what) {
                    case NEWSYMBOLS_MESSAGE:
                        getAppConfig((ResponseListener) msg.obj);
                        break;
                    case REQUEST_BACKUP_DOMAIN:
                        requestBackupDomainJson();
                        break;
                }
            }
        };

    }

    /**
     * 请求备用域名Json配置文件方案
     */
    private void requestBackupDomainJson() {
        Config config = BhexSdk.getConfig();
        if (config == null) {
            return;
        }
        String domainReqUrl = config.getDomainReqUrl();
        if (TextUtils.isEmpty(domainReqUrl)) {
            return;
        }
        ConfigApi.requestBackupDomain(domainReqUrl, new SimpleResponseListener<BackupDomainResponse>() {
            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(BackupDomainResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response)) {
                    String domainsStr = response.getDomains();
                    loadBackupDomains(response.getOrgId(), response.getResponseRandomKey(), domainsStr);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    //初始化配置管理器，加载本地缓存数据
    public void Init() {

        try {
            //1. 加载Config币对列表本地缓存数据
            String newSymbolsString = mmkvSymbols.decodeString(PREFERENCE_NEW_SYMBOLS, "");
            if (!newSymbolsString.isEmpty()) {
                Gson gson = new Gson();
                NewCoinPairListBean cacheConfigData = gson.fromJson(newSymbolsString, new TypeToken<NewCoinPairListBean>() {
                }.getType());
                if (cacheConfigData != null) {
                    mNewCoinPairListBean = cacheConfigData;
                    //赋值orgId
                    orgId = mNewCoinPairListBean.orgId;
                    if (TextUtils.isEmpty(orgId)) {
                        //缓存没有数据或者数据异常 直接checkSum置空，从新获取
                        checkSum = "";
                        return;
                    }
                    checkSum = mNewCoinPairListBean.checksum;
                    realtimeInterval = mNewCoinPairListBean.realtimeInterval;
                    //设置默认币币交易币对
                    if (mDefaultBBTradeCoinPair == null && mNewCoinPairListBean.symbol != null && mNewCoinPairListBean.symbol.size() > 0) {
                        for (CoinPairBean coinPairBean : mNewCoinPairListBean.symbol) {
                            coinPairBean.setCoinType(COIN_TYPE.COIN_TYPE_BB.getCoinType());
                        }
                        mDefaultBBTradeCoinPair = mNewCoinPairListBean.symbol.get(0);
                    }
                    //设置默认期权交易币对
                    if (mDefaultOptionTradeCoinPair == null && mNewCoinPairListBean.optionSymbol != null && mNewCoinPairListBean.optionSymbol.size() > 0) {
                        for (CoinPairBean coinPairBean : mNewCoinPairListBean.optionSymbol) {
                            coinPairBean.setCoinType(COIN_TYPE.COIN_TYPE_OPTION.getCoinType());
                        }
                        mDefaultOptionTradeCoinPair = mNewCoinPairListBean.optionSymbol.get(0);
                    }
                    //设置默认合约交易币对
                    if (mDefaultFuturesTradeCoinPair == null && mNewCoinPairListBean.futuresSymbol != null && mNewCoinPairListBean.futuresSymbol.size() > 0) {
                        for (CoinPairBean coinPairBean : mNewCoinPairListBean.futuresSymbol) {
                            coinPairBean.setCoinType(COIN_TYPE.COIN_TYPE_PERPETUAL_CONTRACT.getCoinType());
                        }
                        //设置默认交易币种 期货
                        mDefaultFuturesTradeCoinPair = mNewCoinPairListBean.futuresSymbol.get(0);
                    }

                    if (mDefaultMarginTradeCoinPair == null && mNewCoinPairListBean.marginSymbol != null && mNewCoinPairListBean.marginSymbol.size() > 0) {
                        for (CoinPairBean coinPairBean : mNewCoinPairListBean.marginSymbol) {
                            //统一设置币币列表--币种类型
                            coinPairBean.setCoinType(COIN_TYPE.COIN_TYPE_MARGIN.getCoinType());
                        }
                        //币币-设置默认交易币种
                        mDefaultMarginTradeCoinPair = mNewCoinPairListBean.marginSymbol.get(0);
                    }
                    if (mNewCoinPairListBean == null) {
                        /**添加一下自选配置*********/
                        List<CoinPairBean> bbSymbolsList = mNewCoinPairListBean.symbol;
                        if (bbSymbolsList != null && bbSymbolsList.size() > 0) {
                            mDefaultMarginTradeCoinPair = bbSymbolsList.get(0);
                            for (CoinPairBean bbSymbol : bbSymbolsList) {
                                //循环设置币种类型为币币
                                bbSymbol.setCoinType(COIN_TYPE.COIN_TYPE_BB.getCoinType());
                            }
                        }
                    }

                    //加载币对列表到内存中
                    loadSymbolsToMap(mNewCoinPairListBean);
                    //加载token到内存中
                    loadTokenItemToMap(mNewCoinPairListBean);
                    //本地收藏
                    loadLocalFavorites();

                    if (!BasicFunctionsManager.getInstance().getBasicFunctionsConfig().isMargin()) {//判断开启了杠杆
                        //加载margin token缓存数据
                        String marginTokensJosn = mmkvSymbols.decodeString(PREFERENCE_MARGIN_TOKENS, "");
                        if (!TextUtils.isEmpty(marginTokensJosn)) {
                            MarginTokenConfigResponse marginTokenConfigResponse = Convert.fromJson(marginTokensJosn, MarginTokenConfigResponse.class);
                            loadMarginTokenItemToMap(marginTokenConfigResponse, false);
                        }

                        //请求杠杆token 列表
                        requestMarginTokens();
                    }
                }
            }
        } catch (JsonSyntaxException e) {
            e.printStackTrace();
        }

        timer = new Timer();
        task = new TimerTask() {
            @Override
            public void run() {
                mHandler.sendEmptyMessage(NEWSYMBOLS_MESSAGE);
            }
        };

        timer.schedule(task, 0, nPeriod);

        if (UserInfo.isLogin()) {
            //登录了获取自选
            AppConfigManager.GetInstance().requestFavorites();
        }
    }

    /**
     * 请求杠杆token 列表
     */
    //请求杠杆交易的代币配置列表
    private void requestMarginTokens() {
        MarginApi.getMarginTokenConfig("", new SimpleResponseListener<MarginTokenConfigResponse>() {
            @Override
            public void onBefore() {
                super.onBefore();
            }

            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(MarginTokenConfigResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response)) {
                    loadMarginTokenItemToMap(response, true);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    //释放资源，取消定时器和任务
    public void release() {
        if (task != null) {
            task.cancel();
        }
        if (timer != null) {
            timer.purge();
            timer.cancel();
        }
        mInstance = null;
    }

    /**
     * 保存币种到本地
     *
     * @param response
     */
    //保存币对数据到本地缓存
    private void saveNewSymbolsToNative(NewCoinPairListBean response) {
        try {
            if (response != null) {
                Gson gson = new Gson();
                String gsonString = gson.toJson(response, new TypeToken<NewCoinPairListBean>() {
                }.getType());
                if (gsonString != null && !"".equals(gsonString)) {
                    mmkvSymbols.encode(PREFERENCE_NEW_SYMBOLS, gsonString);
                    //判断coin是否在 futuresSymbol中
                    //resetContractTradeCoinValue(response.futuresSymbol);
                    //判断coin是否在 futuresSymbol中
                    //resetOptionTradeCoinValue(response.optionSymbol);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    //重置合约交易币对值，用于多语言适配
    public void resetContractTradeCoinValue(List<CoinPairBean> futuresSymbol) {

        String tradeCoin = SPEx.get("contractTradeCoin", "");
        if (TextUtils.isEmpty(tradeCoin)) {
            return;
        }

        CoinPairBean coin = Convert.fromJson(tradeCoin, CoinPairBean.class);

        for (int i = 0; i < futuresSymbol.size(); i++) {
            CoinPairBean item = futuresSymbol.get(i);
            if (item.isEquals(coin)) {
                //保存信息
                String coinJson = Convert.toJson(item);
                DebugLog.d("ABBB====>", "coin_json==" + coinJson);
                //LongLog.loge("ABBB====>",);
                SPEx.set("contractTradeCoin", coinJson);

                return;
            }
        }
    }


    //重置期权交易币对值，用于多语言适配
    public void resetOptionTradeCoinValue(List<CoinPairBean> futuresSymbol) {

        String tradeCoin = SPEx.get("optionTradeCoin", "");
        if (TextUtils.isEmpty(tradeCoin)) {
            return;
        }

        CoinPairBean coin = Convert.fromJson(tradeCoin, CoinPairBean.class);

        for (int i = 0; i < futuresSymbol.size(); i++) {
            CoinPairBean item = futuresSymbol.get(i);
            if (item.isEquals(coin)) {
                //保存信息
                String coinJson = Convert.toJson(item);
                DebugLog.d("ABBB====>", "coin_json==" + coinJson);
                //LongLog.loge("ABBB====>",);
                SPEx.set("optionTradeCoin", coinJson);

                return;
            }
        }
    }


    //设置登录状态变化观察者
    public void setLoginChangeObserver(LoginstatusChange observer) {
        if (observer != null) mObserverList.add(observer);
    }

    //设置期权币种变化观察者
    public void setOptionSymbolsChangeObserver(OptionSymbolsChange observer) {
        if (observer != null) mOptionSymbolsChangeList.add(observer);
    }

    //设置合约币种变化观察者
    public void setFuturesSymbolsChangeObserver(FuturesSymbolsChange observer) {
        if (observer != null) mFuturesSymbolsChangeList.add(observer);
    }

    /**
     * 获取币币作价单位的token id list
     *
     * @return
     */
    //获取作价单位的代币ID列表
    public List<String> getQuoteCoinList() {
        List<String> tokenList = new ArrayList<>();
        List<QuoteTokensBean.TokenItem> quoteTokenList = getQuoteTokenList();
        if (quoteTokenList != null) {
            for (QuoteTokensBean.TokenItem tokenItem : quoteTokenList) {
                if (tokenItem != null) {
                    String tokenId = tokenItem.getTokenId();
                    if (!TextUtils.isEmpty(tokenId)) {
                        tokenList.add(tokenId);
                    }
                }
            }
        }

        return tokenList;
    }

    /**
     * 获取币币token的token id list
     *
     * @return
     */
    //获取币币交易的代币ID列表
    public List<String> getBBTokensList() {
        List<String> tokenList = new ArrayList<>();
        List<QuoteTokensBean.TokenItem> quoteTokenList = getTokenList();
        if (quoteTokenList != null) {
            for (QuoteTokensBean.TokenItem tokenItem : quoteTokenList) {
                if (tokenItem != null) {
                    String tokenId = tokenItem.getTokenId();
                    if (!TextUtils.isEmpty(tokenId)) {
                        tokenList.add(tokenId);
                    }
                }
            }
        }

        return tokenList;
    }

    //获取作价代币列表
    public List<QuoteTokensBean.TokenItem> getQuoteTokenList() {
        if (mNewCoinPairListBean != null && mNewCoinPairListBean.customQuoteToken != null) {
            return mNewCoinPairListBean.customQuoteToken;
        } else return null;

    }

    //获取代币列表
    public List<QuoteTokensBean.TokenItem> getTokenList() {
        if (mNewCoinPairListBean != null && mNewCoinPairListBean.token != null) {
            return mNewCoinPairListBean.token;
        } else return null;

    }

    //根据代币ID获取代币信息
    public QuoteTokensBean.TokenItem getToken(String tokenId) {
        if (tokenId != null) {
            return tokenMap.get(tokenId);
        }
        return null;
    }

    /**
     * 获取期权板分类信息
     *
     * @return
     */
    //获取期权板块分类信息列表
    public List<OptionCategoryBean> getOptionCategoryList() {
        if (mNewCoinPairListBean != null) {
            List<OptionCategoryBean> optionCategoryList = mNewCoinPairListBean.optionUnderlying;
            return optionCategoryList;
        }
        return null;
    }

    /**
     * 获取期权板分类信息
     *
     * @return
     */
    //获取期权板块分类信息Map
    public HashMap<String, OptionCategoryBean> getOptionCategoryMap() {
        return optionCategoryMap;
    }

    /**
     * 根据分类id获取期权板信息
     *
     * @param boardId
     * @return
     */
    //根据板块ID获取期权板块信息
    public OptionCategoryBean getOptionCategoryMap(String boardId) {
        OptionCategoryBean optionCategoryBean = optionCategoryMap.get(boardId);
        return optionCategoryBean;
    }

    /**
     * 获取App各种配置信息，包括币对列表
     */
    //获取应用的各种配置信息，包括币对列表
    public void getAppConfig(final ResponseListener listener) {

        if (mNewCoinPairListBean != null && listener != null) {
            //handler循环更新 listener是空，不走此处，不会直接回调数据
            listener.onSuccess(mNewCoinPairListBean);
            if (bCacheNewSymbols == false) return;
        }

        GetParams params = BParamsBuilder.get()
//                .url(Urls.API_NEW_SYMBOLS)
                .url(Urls.API_BASIC_APP_CONFIG_URL).addParam("type", "all")     //all coin option futures
                .addParam("without_country", "true") //是否包含国家信息
                .addParam("checksum", "")
//                .addParam("checksum",checkSum)
                .addParam("moduleTypes", "1,2,3,4")   //1-中部宫格导航 2-底部tab导航 3-开屏图片 4-home logo, 多个用,分开。如：1,2,4
//                .addParam("lightModel",1)   //1-日间模式 2-夜间模式
                .addParam("app_id", DevicesUtil.getPackageName(BhexSdk.getContext())).addParam("app_version", DevicesUtil.getAppVersionName(BhexSdk.getContext())).addParam("device_type", "android").addParam("device_version", DevicesUtil.getSdkVersion(BhexSdk.getContext())).build();
        HttpUtils.getInstance().request(params, NewCoinPairListBean.class, new SimpleResponseListener<NewCoinPairListBean>() {
            @Override
            public void onFinish() {
                super.onFinish();
                if (listener != null) listener.onFinish();
            }

            @Override
            public void onSuccess(NewCoinPairListBean response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    if (!response.updated) {
                        //没有数据更新 直接return
                        return;
                    }
                    if (!TextUtils.isEmpty(response.orgId)) {
                        orgId = response.orgId;
                    }
                    checkSum = response.checksum;
                    realtimeInterval = response.realtimeInterval;
                    /**添加一下自选配置*********/
                    List<CoinPairBean> bbSymbolsList = response.symbol;
                    /** 处理币对数据-对应设置币对类型 和 默认交易币种 ********************/
                    if (bbSymbolsList != null && bbSymbolsList.size() > 0) {
                        for (CoinPairBean coinPairBean : bbSymbolsList) {
                            //统一设置币币列表--币种类型
                            coinPairBean.setCoinType(COIN_TYPE.COIN_TYPE_BB.getCoinType());
                        }
                        //币币-设置默认交易币种
                        if (mDefaultBBTradeCoinPair == null) {
                            mDefaultBBTradeCoinPair = bbSymbolsList.get(0);
                        }
                    }

                    if (mDefaultMarginTradeCoinPair == null && response.marginSymbol != null && response.marginSymbol.size() > 0) {
                        for (CoinPairBean coinPairBean : response.marginSymbol) {
                            //统一设置币币列表--币种类型
                            coinPairBean.setCoinType(COIN_TYPE.COIN_TYPE_MARGIN.getCoinType());
                        }
                        //币币-设置默认交易币种
                        mDefaultMarginTradeCoinPair = response.marginSymbol.get(0);
                    }
                    if (response.optionSymbol != null && response.optionSymbol.size() > 0) {
                        for (CoinPairBean coinPairBean : response.optionSymbol) {
                            coinPairBean.setCoinType(COIN_TYPE.COIN_TYPE_OPTION.getCoinType());
                        }
                        //期权-设置默认交易币种
                        if (mDefaultOptionTradeCoinPair == null) {
                            mDefaultOptionTradeCoinPair = response.optionSymbol.get(0);
                        }
                    }
                    if (response.futuresSymbol != null && response.futuresSymbol.size() > 0) {
                        for (CoinPairBean coinPairBean : response.futuresSymbol) {
                            coinPairBean.setCoinType(COIN_TYPE.COIN_TYPE_PERPETUAL_CONTRACT.getCoinType());
                        }
                        //期货-设置默认交易币种
                        if (mDefaultFuturesTradeCoinPair == null) {
                            mDefaultFuturesTradeCoinPair = response.futuresSymbol.get(0);
                        }
                    }

                    //缓存币对到本地
                    saveNewSymbolsToNative(response);

                    //加载币对列表到内存中
                    loadSymbolsToMap(response);
                    //加载token到内存中
                    loadTokenItemToMap(response);

                    mNewCoinPairListBean = response;

                    //期权-币种有变化，提示币种变化
                    if (mNewCoinPairListBean != null && mNewCoinPairListBean.optionSymbol != null && !getSymbolsListStr(mNewCoinPairListBean.optionSymbol).equalsIgnoreCase(getSymbolsListStr(response.optionSymbol))) {
                        if (mOptionSymbolsChangeList != null && mOptionSymbolsChangeList.size() > 0) {
                            for (OptionSymbolsChange observer : mOptionSymbolsChangeList) {
                                observer.onOptionSymbolsChange();
                            }
                        }
                    }
                    //期货-币种有变化，提示币种变化
                    if (mNewCoinPairListBean != null && mNewCoinPairListBean.futuresSymbol != null && !getSymbolsListStr(mNewCoinPairListBean.futuresSymbol).equalsIgnoreCase(getSymbolsListStr(response.futuresSymbol))) {
                        if (mFuturesSymbolsChangeList != null && mFuturesSymbolsChangeList.size() > 0) {
                            for (FuturesSymbolsChange observer : mFuturesSymbolsChangeList) {
                                observer.onFuturesSymbolsChange();
                            }
                        }
                    }

                    bCacheNewSymbols = false;

                    loadBackupDomains(response.orgId, response.responseRandomKey, response.domains);

                    //回调结果
                    if (listener != null) {
                        listener.onSuccess(response);
                    }

                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                //接口失败了，重新获取
                Message sendMessage = new Message();
                sendMessage.what = NEWSYMBOLS_MESSAGE;
                sendMessage.obj = listener;
                mHandler.sendMessageDelayed(sendMessage, AppData.Config.TIMER_PERIOD_DEFAULT_VALUE);
                mHandler.sendEmptyMessageDelayed(REQUEST_BACKUP_DOMAIN, 2000);
            }

        });

    }

    /**
     * 更新域名换成列表
     *
     * @param orgId
     * @param responseRandomKey
     * @param domains
     */
    //加载备用域名列表
    private void loadBackupDomains(String orgId, String responseRandomKey, String domains) {
        try {
            Config config = BhexSdk.getConfig();
            if (TextUtils.isEmpty(orgId)) {
                if (config != null) {
                    orgId = config.getOrdId();
                }
            }
            if (TextUtils.isEmpty(responseRandomKey)) {
                if (config != null) {
                    responseRandomKey = config.getDomainsKey();
                }
            }
            if (!TextUtils.isEmpty(domains) && !TextUtils.isEmpty(orgId) && !TextUtils.isEmpty(responseRandomKey)) {
                String domainsKey = orgId + "." + responseRandomKey;
                if (!TextUtils.isEmpty(domainsKey)) {
                    String json = SignUtils.decryptDataWithAES(domainsKey, domains);
                    BackupDomainList backupDomains = JsonConvertor.getInstance().fromJson(json, BackupDomainList.class);

                    BackupDomainManager.getInstance().saveBackupDomainList(backupDomains);
                    DebugLog.e("domainList: " + json);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();

        }
    }

    /**
     * 获取orgId
     *
     * @return
     */
    //获取组织ID
    public String getOrgId() {
        if (TextUtils.isEmpty(orgId)) {
            if (mNewCoinPairListBean != null) {
                orgId = mNewCoinPairListBean.orgId;
            }
        }
        return orgId;
    }

    //获取是否检查邀请码的配置
    public boolean getCheckInviteCode() {
        if (mNewCoinPairListBean != null) {
            return mNewCoinPairListBean.checkInviteCode;
        }
        return false;
    }


    /**
     * 获取realtimeInterval
     *
     * @return
     */
    //获取实时数据更新间隔
    public String getRealtimeInterval() {
        if (TextUtils.isEmpty(realtimeInterval)) {
            if (mNewCoinPairListBean != null) {
                realtimeInterval = mNewCoinPairListBean.realtimeInterval;
            }
        }
        return realtimeInterval;
    }

    /**
     * 加载币币 token进Map
     *
     * @param response
     */
    //加载代币信息到内存Map中
    private void loadTokenItemToMap(NewCoinPairListBean response) {
        if (response != null) {
            if (response.token != null) {
                HashMap<String, QuoteTokensBean.TokenItem> newTokenMap = new HashMap<>();
                for (QuoteTokensBean.TokenItem tokenItem : response.token) {
                    newTokenMap.put(tokenItem.getTokenId(), tokenItem);
                }
                tokenMap = newTokenMap;
            }
        }
    }

    /**
     * 获取token信息
     *
     * @param tokenId
     * @return
     */
    //根据代币ID获取代币详细信息
    public QuoteTokensBean.TokenItem getTokenItemByTokenId(String tokenId) {
        return tokenMap.get(tokenId);
    }

    /**
     * 加载杠杆 token进Map
     *
     * @param response
     * @param isNewReq 是否是新请求的数据
     */
    //加载杠杆交易代币信息到内存Map中
    public void loadMarginTokenItemToMap(MarginTokenConfigResponse response, boolean isNewReq) {
        if (response != null) {
            List<MarginTokenConfigResponse.MarginToken> marginTokens = response.getArray();
            if (marginTokens != null) {
                HashMap<String, MarginTokenConfigResponse.MarginToken> newTokenMap = new HashMap<>();
                for (MarginTokenConfigResponse.MarginToken tokenItem : marginTokens) {
                    newTokenMap.put(tokenItem.getTokenId(), tokenItem);
                }
                marginTokenMap = newTokenMap;
            }
            if (isNewReq) {
                String marginTokensJson = Convert.toJson(response);
                if (TextUtils.isEmpty(marginTokensJson)) {
                    mmkvSymbols.encode(PREFERENCE_MARGIN_TOKENS, marginTokensJson);
                }
            }
        }
    }

    /**
     * 获取token信息
     *
     * @param tokenId
     * @return
     */
    //根据代币ID获取杠杆交易代币信息
    public MarginTokenConfigResponse.MarginToken getMarginTokenItemByTokenId(String tokenId) {
        return marginTokenMap.get(tokenId);
    }

    //获取所有杠杆交易代币列表
    public List<MarginTokenConfigResponse.MarginToken> getMarginTokens() {
        return new ArrayList<>(marginTokenMap.values());
    }

    /**
     * 获取token 链路类型
     *
     * @param tokenId
     * @return
     */
    //根据代币ID获取代币的链路类型列表
    public List<ChainType> getTokenChainTypesByTokenId(String tokenId) {
        QuoteTokensBean.TokenItem tokenItem = getTokenItemByTokenId(tokenId);
        if (tokenItem != null) {
            List<ChainType> chainTypes = tokenItem.getChainTypes();
            return chainTypes;
        }
        return null;
    }

    /**
     * 加载币对列表到内存中，方便各个地方使用
     *
     * @param response
     */
    //加载币对列表到内存中，方便各个地方使用
    private void loadSymbolsToMap(NewCoinPairListBean response) {
        if (response != null) {
            //币币币对
            List<CoinPairBean> bbSymbolsList = response.symbol;
            if (bbSymbolsList != null && bbSymbolsList.size() > 0) {
//                symbolMap.clear();
//                symbolDigitMap.clear();
                HashMap<String, CoinPairBean> newSymbolMap = new HashMap<>();
                HashMap<String, Integer> newSymbolDigitMap = new HashMap<>();

                for (CoinPairBean data : bbSymbolsList) {
                    data.setCoinType(COIN_TYPE.COIN_TYPE_BB.getCoinType());
                    String basePrecision = data.getBasePrecision();
//                    basePrecision = "10";
                    if (!TextUtils.isEmpty(basePrecision)) {
                        int baseDigit = NumberUtils.calNumerCount(null, basePrecision);
                        data.setBaseDigit(baseDigit);
                        newSymbolDigitMap.put(data.getSymbolId() + data.getBaseTokenId(), baseDigit);
                    }
                    String quotePrecision = data.getQuotePrecision();
                    if (!TextUtils.isEmpty(quotePrecision)) {
                        int quoteDigit = NumberUtils.calNumerCount(null, quotePrecision);
                        data.setAmountDigit(quoteDigit);
                        newSymbolDigitMap.put("amount-" + data.getSymbolId() + data.getQuoteTokenId(), quoteDigit);
                    }
                    String minPricePrecision = data.getMinPricePrecision();
                    if (!TextUtils.isEmpty(minPricePrecision)) {
                        int priceDigit = NumberUtils.calNumerCount(null, minPricePrecision);
                        data.setPriceDigit(priceDigit);
                        newSymbolDigitMap.put(data.getSymbolId() + data.getQuoteTokenId(), priceDigit);
                    }

                    String symbolId = data.getSymbolId();
                    newSymbolMap.put(symbolId, data);
                }
                symbolDigitMap = newSymbolDigitMap;
                symbolMap = newSymbolMap;
            }

            //期货币对
            List<CoinPairBean> futuresSymbolList = response.futuresSymbol;
            if (futuresSymbolList != null && futuresSymbolList.size() > 0) {
                HashMap<String, CoinPairBean> newFuturesSymbolMap = new HashMap<>();
                HashMap<String, Integer> newFuturesSymbolDigitMap = new HashMap<>();
                for (CoinPairBean data : futuresSymbolList) {
                    data.setCoinType(COIN_TYPE.COIN_TYPE_PERPETUAL_CONTRACT.getCoinType());
                    String basePrecision = data.getBasePrecision();
                    if (!TextUtils.isEmpty(basePrecision)) {
                        int baseDigit = NumberUtils.calNumerCount(null, basePrecision);
                        data.setBaseDigit(baseDigit);
                        newFuturesSymbolDigitMap.put(data.getSymbolId() + data.getBaseTokenId(), baseDigit);
                    }
                    String quotePrecision = data.getQuotePrecision();
                    if (!TextUtils.isEmpty(quotePrecision)) {
                        int quoteDigit = NumberUtils.calNumerCount(null, quotePrecision);
                        data.setAmountDigit(quoteDigit);
                        newFuturesSymbolDigitMap.put("amount-" + data.getSymbolId() + data.getQuoteTokenId(), quoteDigit);
                    }
                    String minPricePrecision = data.getMinPricePrecision();
                    if (!TextUtils.isEmpty(minPricePrecision)) {
                        int priceDigit = NumberUtils.calNumerCount(null, minPricePrecision);
                        data.setPriceDigit(priceDigit);
                        newFuturesSymbolDigitMap.put(data.getSymbolId() + data.getQuoteTokenId(), priceDigit);
                    }

                    String symbolId = data.getSymbolId();
                    newFuturesSymbolMap.put(symbolId, data);
                }
                futuresSymbolMap = newFuturesSymbolMap;
                futuresSymbolDigitMap = newFuturesSymbolDigitMap;
            }

            //期权币对分类：主板 创新板
            List<OptionCategoryBean> categoryList = response.optionUnderlying;
            if (categoryList != null && categoryList.size() > 0) {
                HashMap<String, OptionCategoryBean> newOptionCategoryMap = new HashMap<>();
                for (OptionCategoryBean optionCategoryBean : categoryList) {
                    newOptionCategoryMap.put(optionCategoryBean.getId(), optionCategoryBean);
                }
                optionCategoryMap = newOptionCategoryMap;
            }

            //期权币对Symbol
            List<CoinPairBean> optionDatas = response.optionSymbol;
            if (optionDatas != null && optionDatas.size() > 0) {

                HashMap<String, CoinPairBean> newOptionSymbolMap = new HashMap<>();
                HashMap<String, Integer> newOptionSymbolDigitMap = new HashMap<>();
                for (CoinPairBean data : optionDatas) {
                    data.setCoinType(COIN_TYPE.COIN_TYPE_OPTION.getCoinType());
                    String basePrecision = data.getBasePrecision();
                    if (!TextUtils.isEmpty(basePrecision)) {
                        int baseDigit = NumberUtils.calNumerCount(null, basePrecision);
                        data.setBaseDigit(baseDigit);
                        newOptionSymbolDigitMap.put(data.getSymbolId() + data.getBaseTokenId(), baseDigit);
                    }
                    String quotePrecision = data.getQuotePrecision();
                    if (!TextUtils.isEmpty(quotePrecision)) {
                        int quoteDigit = NumberUtils.calNumerCount(null, quotePrecision);
                        data.setAmountDigit(quoteDigit);
                        newOptionSymbolDigitMap.put("amount-" + data.getSymbolId() + data.getQuoteTokenId(), quoteDigit);
                    }
                    String minPricePrecision = data.getMinPricePrecision();
                    if (!TextUtils.isEmpty(minPricePrecision)) {
                        int priceDigit = NumberUtils.calNumerCount(null, minPricePrecision);
                        data.setPriceDigit(priceDigit);
                        newOptionSymbolDigitMap.put(data.getSymbolId() + data.getQuoteTokenId(), priceDigit);
                    }

                    String symbolId = data.getSymbolId();
                    newOptionSymbolMap.put(symbolId, data);
                }
                optionSymbolDigitMap = newOptionSymbolDigitMap;
                optionSymbolMap = newOptionSymbolMap;
            }

            //杠杆币对
            List<CoinPairBean> marginSymbolList = response.marginSymbol;
            if (marginSymbolList != null && marginSymbolList.size() > 0) {
                HashMap<String, CoinPairBean> newMarginSymbolMap = new HashMap<>();
                HashMap<String, Integer> newMarginSymbolDigitMap = new HashMap<>();
                for (CoinPairBean data : marginSymbolList) {
                    data.setCoinType(COIN_TYPE.COIN_TYPE_MARGIN.getCoinType());
                    String basePrecision = data.getBasePrecision();
                    if (!TextUtils.isEmpty(basePrecision)) {
                        int baseDigit = NumberUtils.calNumerCount(null, basePrecision);
                        data.setBaseDigit(baseDigit);
                        newMarginSymbolDigitMap.put(data.getSymbolId() + data.getBaseTokenId(), baseDigit);
                    }
                    String quotePrecision = data.getQuotePrecision();
                    if (!TextUtils.isEmpty(quotePrecision)) {
                        int quoteDigit = NumberUtils.calNumerCount(null, quotePrecision);
                        data.setAmountDigit(quoteDigit);
                        newMarginSymbolDigitMap.put("amount-" + data.getSymbolId() + data.getQuoteTokenId(), quoteDigit);
                    }
                    String minPricePrecision = data.getMinPricePrecision();
                    if (!TextUtils.isEmpty(minPricePrecision)) {
                        int priceDigit = NumberUtils.calNumerCount(null, minPricePrecision);
                        data.setPriceDigit(priceDigit);
                        newMarginSymbolDigitMap.put(data.getSymbolId() + data.getQuoteTokenId(), priceDigit);
                    }

                    String symbolId = data.getSymbolId();
                    newMarginSymbolMap.put(symbolId, data);
                }
                mMarginSymbolMap = newMarginSymbolMap;
                marginSymbolDigitMap = newMarginSymbolDigitMap;
            }
        }
    }

    /**
     * 获取币币symbols
     *
     * @return
     */
    //获取币币交易币对Map
    public HashMap<String, CoinPairBean> getBBSymbolMap() {
        return symbolMap;
    }

    /**
     * 获取合约symbols
     *
     * @return
     */
    //获取合约交易币对Map
    public HashMap<String, CoinPairBean> getContractSymbolMap() {
        return futuresSymbolMap;
    }

    /**
     * 获取期权symbols
     *
     * @return
     */
    //获取期权交易币对Map
    public HashMap<String, CoinPairBean> getOptionSymbolMap() {
        return optionSymbolMap;
    }

    /**
     * 获取币对实体信息
     *
     * @param id
     * @return
     */
    //根据ID获取币对详细信息
    public CoinPairBean getSymbolInfoById(String id) {
        if (symbolMap.containsKey(id)) {
            return symbolMap.get(id);
        } else if (optionSymbolMap.containsKey(id)) {
            return optionSymbolMap.get(id);
        } else if (futuresSymbolMap.containsKey(id)) {
            return futuresSymbolMap.get(id);
        } else {
            return null;
        }
    }

    /**
     * 获取杠杆币对信息
     *
     * @param id
     * @return
     */
    //根据ID获取杠杆交易币对信息
    public CoinPairBean getMarginSymbolInfoById(String id) {
        if (mMarginSymbolMap.containsKey(id)) {
            return mMarginSymbolMap.get(id);
        } else {
            return null;
        }
    }

    /**
     * 获取期货币对实体信息
     *
     * @param id
     * @return
     */
    //根据ID获取期货合约币对信息
    public CoinPairBean getFuturesSymbolInfoById(String id) {
        if (futuresSymbolMap.containsKey(id)) {
            return futuresSymbolMap.get(id);
        } else {
            return null;
        }
    }

    //根据代币名称查找币对信息
    public CoinPairBean getSymbolByToken(String token) {
        if (symbolMap != null) {
            for (Map.Entry<String, CoinPairBean> entry : symbolMap.entrySet()) {
                if (entry.getKey().startsWith(token)) return entry.getValue();
            }
            for (Map.Entry<String, CoinPairBean> entry : symbolMap.entrySet()) {

                if (entry.getKey().contains(token)) return entry.getValue();
            }
        }
//        if(optionSymbolMap != null) {
//            for (Map.Entry<String, CoinPairBean> entry : optionSymbolMap.entrySet()) {
//                if(entry.getKey().startsWith(token))
//                    return entry.getValue();
//            }
//            for (Map.Entry<String, CoinPairBean> entry : optionSymbolMap.entrySet()) {
//                if(entry.getKey().contains(token))
//                    return entry.getValue();
//            }
//        }
        return null;
    }

    //根据代币名称查找杠杆交易币对信息
    public CoinPairBean getMarginSymbolByToken(String token) {
        if (mMarginSymbolMap != null) {
            for (Map.Entry<String, CoinPairBean> entry : mMarginSymbolMap.entrySet()) {
                if (entry.getKey().startsWith(token)) return entry.getValue();
            }
            for (Map.Entry<String, CoinPairBean> entry : mMarginSymbolMap.entrySet()) {

                if (entry.getKey().contains(token)) return entry.getValue();
            }
        }
//        if(optionSymbolMap != null) {
//            for (Map.Entry<String, CoinPairBean> entry : optionSymbolMap.entrySet()) {
//                if(entry.getKey().startsWith(token))
//                    return entry.getValue();
//            }
//            for (Map.Entry<String, CoinPairBean> entry : optionSymbolMap.entrySet()) {
//                if(entry.getKey().contains(token))
//                    return entry.getValue();
//            }
//        }
        return null;
    }

    /**
     * 根据 symbolId + tokenId 获取token小数位数
     *
     * @param id
     * @return 如果token列表里没有查询到，则返回默认值
     */
    //根据币对ID和代币ID获取数量的小数位数
    public int getAmountDigitBySymbolIdAndTokenId(String id) {
        if (!TextUtils.isEmpty(id) && symbolDigitMap.containsKey(id)) {
            return symbolDigitMap.get("amount-" + id);
        } else if (!TextUtils.isEmpty(id) && optionSymbolDigitMap.containsKey(id)) {
            return optionSymbolDigitMap.get("amount-" + id);
        } else if (!TextUtils.isEmpty(id) && futuresSymbolDigitMap.containsKey(id)) {
            return futuresSymbolDigitMap.get("amount-" + id);
        } else {
            return AppData.Config.DIGIT_DEFAULT_VALUE;
        }
    }

    /**
     * 根据 symbolId + tokenId 获取token小数位数
     *
     * @param id
     * @return 如果token列表里没有查询到，则返回默认值
     */
    //根据币对ID和代币ID获取代币的小数位数
    public int getTokenDigitBySymbolIdAndTokenId(String id) {
        if (!TextUtils.isEmpty(id) && symbolDigitMap.containsKey(id)) {
            return symbolDigitMap.get(id);
        } else if (!TextUtils.isEmpty(id) && optionSymbolDigitMap.containsKey(id)) {
            return optionSymbolDigitMap.get(id);
        } else if (!TextUtils.isEmpty(id) && futuresSymbolDigitMap.containsKey(id)) {
            return futuresSymbolDigitMap.get(id);
        } else {
            return AppData.Config.DIGIT_DEFAULT_VALUE;
        }
    }

    //判断某个币对是否已收藏
    public boolean isFavorite(CoinPairBean coinPairBean) {
        if (coinPairBean != null) {
            String key = coinPairBean.getExchangeId() + "." + coinPairBean.getSymbolId();
            return favoritesMap.containsKey(key);
        }
        return false;
    }

    //取消单个自选币对
    public void cancelLocalFavorite(CoinPairBean cancelItems) {
        if (cancelItems != null) {
            if (!TextUtils.isEmpty(cancelItems.getSymbolId())) {
                favoritesMap.remove(cancelItems.getExchangeId() + "." + cancelItems.getSymbolId());
            }
            syncFavoriteSort();
        }
    }

    //批量取消自选币对
    public void cancelLocalFavorite(List<CoinPairBean> localFavoriteList) {
        if (symbolMap != null) {
            for (CoinPairBean favoriteBean : localFavoriteList) {
                if (favoriteBean != null) {
                    if (!TextUtils.isEmpty(favoriteBean.getSymbolId())) {
                        favoritesMap.remove(favoriteBean.getExchangeId() + "." + favoriteBean.getSymbolId());
                    }
                }
            }
            syncFavoriteSort();
        }
    }

    //批量取消自选币对（传入Map）
    public void cancelLocalFavorite(HashMap<String, CoinPairBean> cancelMap) {
        if (cancelMap != null && cancelMap.size() > 0) {
            if (symbolMap != null) {
                for (String key : cancelMap.keySet()) {
                    CoinPairBean favoriteSymbol = cancelMap.get(key);
                    if (favoriteSymbol != null) {
                        if (!TextUtils.isEmpty(favoriteSymbol.getSymbolId())) {
                            favoritesMap.remove(favoriteSymbol.getExchangeId() + "." + favoriteSymbol.getSymbolId());
                        }
                    }
                }
                syncFavoriteSort();
            }
        }
    }

    //设置本地自选列表
    public void setLocalFavoriteMap(List<FavoriteBean> localFavoriteList) {
        if (localFavoriteList != null) {
            for (FavoriteBean favoriteBean : localFavoriteList) {
                if (favoriteBean != null) {
                    if (checkHasSymbol(favoriteBean.getSymbolId())) {
                        favoritesMap.put(favoriteBean.getExchangeId() + "." + favoriteBean.getSymbolId(), favoriteBean);
                    }
                }
            }

            EventBus.getDefault().post(new FavoriteChangeEvent());
        }
    }

    /**
     * 检查是否下架了该币对 （注意：如果没有拿到symbol数据，暂不做校验）
     *
     * @param symbolId
     * @return
     */
    //检查是否下架了该币对
    private boolean checkHasSymbol(String symbolId) {
        if (symbolMap != null && symbolMap.size() > 0) {
            return symbolMap.containsKey(symbolId);
        }

        return true;
    }

    /**
     * 自选数据接口同步Server
     */
    //同步自选列表排序到服务器
    private void syncFavoriteSort() {
        List<FavoriteBean> favoriteList = new ArrayList<>();
        for (String key : favoritesMap.keySet()) {
            FavoriteBean favoriteBean = favoritesMap.get(key);
            if (favoriteBean != null) {
                favoriteList.add(favoriteBean);
            }
        }
        FavoriteRecordBean favoriteRecordBean = new FavoriteRecordBean();
        favoriteRecordBean.setData(favoriteList);
        String jsonStr = Convert.toJson(favoriteRecordBean);
        MMKVManager.getInstance().mmkvCacheConfig().encode(AppData.SPKEY.FAVORITE_COINPAIR, jsonStr);
        syncFavoriteSort(jsonStr);
    }

    /**
     * 自选数据接口同步Server
     */
    //同步自选数据到服务器
    private void syncFavoriteSort(String jsonStr) {
        if (!UserInfo.isLogin()) {
            return;
        }
        String newFavoriteSortJson = filterSortJson(jsonStr);
        AccountInfoApi.RequestFavoriteSortSyncRemote(newFavoriteSortJson, new SimpleResponseListener<ResultResponse>() {
            @Override
            public void onBefore() {
                super.onBefore();
            }

            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(ResultResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response)) {

                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });

    }

    //过滤和处理自选排序JSON数据
    private String filterSortJson(String jsonStr) {
        String newJson = "";
        if (!TextUtils.isEmpty(jsonStr)) {
            if (jsonStr.startsWith("{\"data\":")) {
                newJson = jsonStr.replaceFirst("\\{\"data\":", "");
            }

            if (newJson.endsWith("]}")) {
                newJson = newJson.substring(0, newJson.length() - 1);
            }
        } else {
            newJson = "[]";
        }
        return newJson;
    }

    /**
     * 获取 币币-自选
     */
    //请求用户自选列表
    public void requestFavorites() {
        if (!UserInfo.isLogin()) {
            return;
        }
        PostParams params = BParamsBuilder.post().url(Urls.API_FAVORITE_LIST_URL).build();
        HttpUtils.getInstance().request(params, FavoriteListResponse.class, new SimpleResponseListener<FavoriteListResponse>() {
            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(FavoriteListResponse data) {
                super.onSuccess(data);
                if (CodeUtils.isSuccess(data, false)) {

                    NetWorkApiManager.getTradeInstance();//启动tradesocket 需要登录成功

                    List<FavoriteBean> favoriteBeanList = data.getArray();

                    if (favoriteBeanList != null && favoriteBeanList.size() > 0) {
                        setLocalFavoriteMap(favoriteBeanList);
                        //自选同步
                        syncFavoriteSort();
                    }

                    if (mObserverList != null && mObserverList.size() > 0) {
                        for (LoginstatusChange observer : mObserverList) {
                            observer.onFavriateChange();
                        }
                    }
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    /**
     * 登录用户自选数据map
     *
     * @return
     */
    //获取用户自选数据Map
    public Map<String, FavoriteBean> getFavoritesMap() {
        if (favoritesMap == null || favoritesMap.size() < 1) {
            loadLocalFavorites();
        }
        return favoritesMap;
    }

    /**
     * 获取 默认交易币种（杠杆交易）
     *
     * @return
     */
    //获取杠杆交易币对Map
    public HashMap<String, CoinPairBean> getMarginSymbolMap() {
        return mMarginSymbolMap;
    }


    //获取杠杆交易作价代币列表
    public List<QuoteTokensBean.TokenItem> getMarginQuoteToken() {
        return mNewCoinPairListBean.marginQuoteToken;
    }

    /**
     * 获取 默认交易币种（币币交易）
     *
     * @return
     */
    //获取默认币币交易币对
    public CoinPairBean getDefaultTradeCoinPair() {
        return mDefaultBBTradeCoinPair;
    }

    /**
     * 获取 默认交易币种（杠杆交易）
     *
     * @return
     */
    //获取默认杠杆交易币对
    public CoinPairBean getDefaultMarginCoinPair() {
        return mDefaultMarginTradeCoinPair;
    }

    /**
     * 获取 默认交易币种（期权交易）
     *
     * @return
     */
    //获取默认期权交易币对
    public CoinPairBean getDefaultOptionTradeCoinPair() {
        return mDefaultOptionTradeCoinPair;
    }

    /**
     * 获取 默认交易币种（期货交易）
     *
     * @return
     */
    //获取默认期货交易币对
    public CoinPairBean getDefaultFuturesTradeCoinPair() {
        return mDefaultFuturesTradeCoinPair;
    }

    /**
     * 获取期货-作价token
     *
     * @return
     */
    //获取期货交易作价代币列表
    public List<String> getFuturesCoinToken() {
        if (mNewCoinPairListBean != null) return mNewCoinPairListBean.futuresCoinToken;
        else return null;
    }

    /**
     * 获取期权-作价token
     *
     * @return
     */
    //获取期权交易作价代币列表
    public List<String> getOptionCoinToken() {
        if (mNewCoinPairListBean != null) return mNewCoinPairListBean.optionCoinToken;
        else return null;
    }

    /**
     * 获取体验币-作价token
     *
     * @return
     */
    //获取体验币交易代币列表
    public List<String> getExploreToken() {
        if (mNewCoinPairListBean != null) return mNewCoinPairListBean.exploreToken;
        else return null;
    }

    //体验币Map
    HashMap<String, String> exploreTokenMap = new HashMap<>();

    //获取体验币Map
    public HashMap<String, String> getExploreTokenMap() {
        return exploreTokenMap;
    }

    //将体验币添加到Map中
    public HashMap<String, String> putExploreTokenIntoMap(String tokenName, String tokenId) {
        exploreTokenMap.put(tokenName, tokenId);
        return exploreTokenMap;
    }

    /**
     * 获取期权所有指数tokens BTCUSDT,ETHUSDT
     *
     * @return
     */
    //获取期权的所有指数代币字符串
    public String getAllIndexTokensOfOptions() {
        StringBuffer stringBuffer = new StringBuffer();
        if (mNewCoinPairListBean != null && mNewCoinPairListBean.optionSymbol != null) {
            for (CoinPairBean coinPairBean : mNewCoinPairListBean.optionSymbol) {
                if (coinPairBean.baseTokenOption != null) {
                    String indexToken = coinPairBean.baseTokenOption.indexToken;
                    if (!stringBuffer.toString().contains(indexToken)) {
                        if (stringBuffer.toString().length() > 0) {
                            stringBuffer.append("," + indexToken);
                        } else {
                            stringBuffer.append(indexToken);
                        }
                    }
                }
            }
        }
        return stringBuffer.toString();
    }

    /**
     * 获取合约期货所有指数tokens BTCUSDT,ETHUSDT
     *
     * @return
     */
    //获取期货合约的所有指数代币字符串
    public String getAllIndexTokensOfFutures() {
        StringBuffer stringBuffer = new StringBuffer();
        if (mNewCoinPairListBean != null && mNewCoinPairListBean.futuresSymbol != null) {
            for (CoinPairBean coinPairBean : mNewCoinPairListBean.futuresSymbol) {
                if (coinPairBean.baseTokenFutures != null) {
                    String displayIndexToken = coinPairBean.baseTokenFutures.getDisplayIndexToken();
                    if (!stringBuffer.toString().contains(displayIndexToken)) {
                        if (stringBuffer.toString().length() > 0) {
                            stringBuffer.append("," + displayIndexToken);
                        } else {
                            stringBuffer.append(displayIndexToken);
                        }
                    }
                }
            }
        }
        return stringBuffer.toString();
    }

    //将币对列表转换为字符串格式
    protected String getSymbolsListStr(List<CoinPairBean> list) {
        StringBuffer stringBuffer = new StringBuffer();
        if (list != null && list.size() > 0) {
            for (CoinPairBean coinPairBean : list) {
                if (coinPairBean != null) {
                    String symbol = coinPairBean.getExchangeId() + "." + coinPairBean.getSymbolId();
                    if (stringBuffer.toString().length() > 0) {
                        stringBuffer.append("," + symbol);
                    } else {
                        stringBuffer.append(symbol);
                    }
                }
            }

        }
        return stringBuffer.toString();
    }

    //将代币列表转换为字符串格式
    protected String getTokenListStr(List<QuoteTokensBean.TokenItem> list) {

        StringBuffer stringBuffer = new StringBuffer();
        if (list != null && list.size() > 0) {
            for (QuoteTokensBean.TokenItem tokenItem : list) {

                String tokenId = tokenItem.getTokenId();
                if (!tokenId.equals(AppData.KEY_FAVORITE)) {
                    if (stringBuffer.toString().length() > 0) {
                        stringBuffer.append("," + tokenId);
                    } else {
                        stringBuffer.append(tokenId);
                    }
                }
            }

        }
        return stringBuffer.toString();
    }

    /**
     * 获取所有tokens 拼接参数
     *
     * @return
     */
    //获取所有代币拼接参数字符串
    protected String getTokenListStr() {
        List<QuoteTokensBean.TokenItem> tokenList = getTokenList();
        if (tokenList != null && tokenList.size() > 0) {
            return getTokenListStr(tokenList);
        } else {
            return "";
        }
    }

    /**
     * 获取所有tokens 拼接参数
     *
     * @return
     */
    //获取所有作价代币拼接参数字符串
    protected String getQuoteTokenListStr() {

        List<QuoteTokensBean.TokenItem> tokenList = getQuoteTokenList();
        if (tokenList != null && tokenList.size() > 0) {
            return getTokenListStr(tokenList);
        } else {
            return "";
        }
    }

    /**
     * 合约帮助地址
     *
     * @return
     */
    //获取合约帮助页面地址
    public String getContractHelpUrl() {
        if (mNewCoinPairListBean != null) {
            return mNewCoinPairListBean.contractHelpUrl;
        }
        return "";
    }

    /**
     * 公告更多地址
     *
     * @return
     */
    //获取公告更多页面地址
    public String getAnnouncementMoreUrl() {
        if (mNewCoinPairListBean != null) {
            return mNewCoinPairListBean.announcementMoreUrl;
        }
        return "";
    }

    /**
     * 获取注册入口配置
     *
     * @return
     */
    //获取注册入口配置选项
    public int getRegisterOption() {
        if (mNewCoinPairListBean != null) {
            return mNewCoinPairListBean.registerOption;
        }
        return AppData.REGISTEROPTION.ALL;//默认所有国家手机和邮箱
    }

    /**
     * 获取功能开关
     *
     * @return
     */
    //获取功能开关配置
    public FunctionsBean getFunctions() {
        if (mNewCoinPairListBean != null) {
            return mNewCoinPairListBean.functions;
        }
        return null;
    }

    /**
     * 获取etf杠杆数据Map
     *
     * @return
     */
    //获取ETF杠杆产品价格数据Map
    public HashMap<String, EtfPriceBean> getETFPriceMap() {
        if (mNewCoinPairListBean != null) {
            return mNewCoinPairListBean.etfPrice;
        }
        return null;
    }

    /**
     * 获取 OTC 付款倒计时最大的超时时间
     *
     * @return
     */
    //获取OTC订单支付超时时间
    public int getOtcOrderPayOverTime() {
        if (mNewCoinPairListBean != null) {
            OtcConfigBean otcConfig = mNewCoinPairListBean.otcConfig;
            if (otcConfig != null) {
                return otcConfig.getCancelTime();
            }
        }
        return 15;  //TODO 默认订单支付超时倒计时时间 15分钟
    }

    /**
     * 判断是否是etf杠杆币对
     *
     * @return
     */
    //判断是否是ETF杠杆币对
    public boolean isETF(String symbolId) {
        HashMap<String, EtfPriceBean> etfPriceMap = getETFPriceMap();
        if (etfPriceMap == null) {
            return false;
        }
        if (etfPriceMap.isEmpty()) {
            return false;
        }
        return etfPriceMap.containsKey(symbolId);
    }

    /**
     * 获取etf杠杆币对信息
     *
     * @return
     */
    //获取ETF杠杆币对价格信息
    public EtfPriceBean getETFPriceInfo(String indicesSymbolId) {
        HashMap<String, EtfPriceBean> etfPriceMap = getETFPriceMap();
        if (etfPriceMap == null) {
            return null;
        }
        if (etfPriceMap.isEmpty()) {
            return null;
        }
        return etfPriceMap.get(indicesSymbolId);
    }

    /**
     * 本地自选加载到内存中
     */
    //加载本地自选列表到内存中
    private void loadLocalFavorites() {
        try {
            if (!MMKVManager.getInstance().mmkvCacheConfig().containsKey(AppData.SPKEY.FAVORITE_COINPAIR)) {
                //首次没有，同步一次旧的sp缓存数据
                String oldFavoriteStr = SP.get(AppData.SPKEY.FAVORITE_COINPAIR, "");
                if (!TextUtils.isEmpty(oldFavoriteStr)) {
                    MMKVManager.getInstance().mmkvCacheConfig().encode(AppData.SPKEY.FAVORITE_COINPAIR, oldFavoriteStr);
                }
            }

            String favoriteStr = MMKVManager.getInstance().mmkvCacheConfig().decodeString(AppData.SPKEY.FAVORITE_COINPAIR, "");
            FavoriteRecordBean favoriteRecordBean = Convert.fromJson(favoriteStr, FavoriteRecordBean.class);
            if (favoriteRecordBean != null) {
                List<FavoriteBean> data = favoriteRecordBean.getData();
                AppConfigManager.GetInstance().setLocalFavoriteMap(data);

            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 保存自选
     */
    //保存单个自选币对
    public void saveFavorite(CoinPairBean favoriteCoinPair) {
        try {
            if (favoriteCoinPair != null) {
                LinkedHashMap<String, FavoriteBean> newFavoritesMap = new LinkedHashMap<>();

                FavoriteBean favorite = new FavoriteBean();
                favorite.setExchangeId(favoriteCoinPair.getExchangeId());
                favorite.setSymbolId(favoriteCoinPair.getSymbolId());
                String favoriteKey = favoriteCoinPair.getExchangeId() + "." + favoriteCoinPair.getSymbolId();
                newFavoritesMap.put(favoriteKey, favorite);

                newFavoritesMap.putAll(favoritesMap);
                favoritesMap = newFavoritesMap;

                FavoriteRecordBean favoriteRecordBean = new FavoriteRecordBean();
                List<FavoriteBean> datas = new ArrayList<>();
                for (String key : favoritesMap.keySet()) {
                    FavoriteBean favoriteBean = favoritesMap.get(key);
                    datas.add(favoriteBean);
                }
                favoriteRecordBean.setData(datas);
                String jsonStrOfFavorite = Convert.toJson(favoriteRecordBean);

                MMKVManager.getInstance().mmkvCacheConfig().encode(AppData.SPKEY.FAVORITE_COINPAIR, jsonStrOfFavorite);

                syncFavoriteSort(jsonStrOfFavorite);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 保存自选
     *
     * @param favoriteWaitAddList
     */
    //批量保存自选币对
    public void saveFavorite(List<CoinPairBean> favoriteWaitAddList) {
        try {
            if (favoriteWaitAddList != null) {
                LinkedHashMap<String, FavoriteBean> newFavoritesMap = new LinkedHashMap<>();
                for (CoinPairBean coinPairBean : favoriteWaitAddList) {
                    if (coinPairBean != null) {
                        String key = coinPairBean.getExchangeId() + "." + coinPairBean.getSymbolId();
                        FavoriteBean favoriteBean = new FavoriteBean();
                        favoriteBean.setSymbolId(coinPairBean.getSymbolId());
                        favoriteBean.setExchangeId(coinPairBean.getExchangeId());
                        newFavoritesMap.put(key, favoriteBean);
//                        favoritesMap.put(key,favoriteBean);
                    }
                }

//                newFavoritesMap.putAll(favoritesMap);
                favoritesMap = newFavoritesMap;

                FavoriteRecordBean favoriteRecordBean = new FavoriteRecordBean();
                List<FavoriteBean> datas = new ArrayList<>();
                for (String key : favoritesMap.keySet()) {
                    FavoriteBean favoriteBean = favoritesMap.get(key);
                    datas.add(favoriteBean);
                }
                favoriteRecordBean.setData(datas);
                String jsonStrOfFavorite = Convert.toJson(favoriteRecordBean);

                MMKVManager.getInstance().mmkvCacheConfig().encode(AppData.SPKEY.FAVORITE_COINPAIR, jsonStrOfFavorite);

                syncFavoriteSort(jsonStrOfFavorite);

            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取自选
     */
    //获取本地自选记录
    public FavoriteRecordBean getFavorite() {
        FavoriteRecordBean favoriteRecordBean;
        try {
            if (!MMKVManager.getInstance().mmkvCacheConfig().containsKey(AppData.SPKEY.FAVORITE_COINPAIR)) {
                //首次没有，同步一次旧的sp缓存数据
                String oldFavoriteStr = SP.get(AppData.SPKEY.FAVORITE_COINPAIR, "");
                if (!TextUtils.isEmpty(oldFavoriteStr)) {
                    MMKVManager.getInstance().mmkvCacheConfig().encode(AppData.SPKEY.FAVORITE_COINPAIR, oldFavoriteStr);
                }
            }

            String favoriteStr = MMKVManager.getInstance().mmkvCacheConfig().decodeString(AppData.SPKEY.FAVORITE_COINPAIR, "");
            favoriteRecordBean = Convert.fromJson(favoriteStr, FavoriteRecordBean.class);
            if (favoriteRecordBean != null) {
                List<FavoriteBean> data = favoriteRecordBean.getData();
                AppConfigManager.GetInstance().setLocalFavoriteMap(data);

                return favoriteRecordBean;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        favoriteRecordBean = new FavoriteRecordBean();
        return favoriteRecordBean;
    }

    /**
     * 检查是否有自选
     *
     * @return
     */
    //检查是否有自选币对
    public boolean checkHasFavoriteSymbols() {
        if (favoritesMap != null && favoritesMap.size() > 0) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 获取自选列表
     *
     * @return
     */
    //获取自选币对列表
    public List<CoinPairBean> getFavoriteSymbols() {
        Map<String, FavoriteBean> favoritesMap = getFavoritesMap();
        ArrayList<CoinPairBean> favoriteSymboleList = new ArrayList<>();
        if (favoritesMap != null && favoritesMap.size() > 0) {
            for (String key : favoritesMap.keySet()) {
                FavoriteBean favoriteBean = favoritesMap.get(key);
                if (favoriteBean != null) {
                    CoinPairBean favoriteSymbol = AppConfigManager.GetInstance().getSymbolInfoById(favoriteBean.getSymbolId());
                    if (favoriteSymbol != null) {
                        favoriteSymboleList.add(favoriteSymbol);
                    }
                }
            }
        }
        return favoriteSymboleList;
    }

}
