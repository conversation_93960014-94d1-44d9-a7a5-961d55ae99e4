/*
 *
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: BParamsBuilder.java
 *   @Date: 11/29/18 3:21 PM
 *   @Author: chenjun
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 *
 */

package io.bhex.baselib.network;

import android.text.TextUtils;

import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.account.UserManager;
import io.bhex.sdk.account.bean.UserInfoBean;
import io.bhex.sdk.data_manager.RateAndLocalManager;
import io.bhex.baselib.constant.Fields;
import io.bhex.baselib.core.CApplication;
import io.bhex.baselib.utils.DevicesUtil;
import io.bhex.baselib.network.params.AbstractParams;
import io.bhex.baselib.network.params.GetParams;
import io.bhex.baselib.network.params.PostParams;


public class BParamsBuilder {
    public static PostParams.Builder<PostParams.Builder, PostParams> post() {
        PostParams.Builder<PostParams.Builder, PostParams> builder = new PostParams.Builder();

        addCommonParams(builder);

        return builder;
    }

    public static GetParams.Builder<GetParams.Builder, GetParams> get() {
        GetParams.Builder<GetParams.Builder, GetParams> builder = new GetParams.Builder<>();

        addCommonParams(builder);

        return builder;
    }

    /**
     * 添加公共及时请求的参数
     *
     * @param builder
     */
    private static void addCommonParams(AbstractParams.Builder builder) {

        //builder.addHeader(Fields.PARAM_NETT, DevicesUtil.GetNetworkType(CApplication.getInstance()));

        String userId = "";
        UserInfoBean userInfo = UserManager.getInstance().getUserInfo();
        if (userInfo != null && !TextUtils.isEmpty(userInfo.getUserId())){
            userId = userInfo.getUserId();
        }

        // 添加调试日志
        android.util.Log.d("BParamsBuilder", "构建请求头 - userInfo: " + (userInfo != null ? "不为空" : "为空") +
                          ", userId: " + (TextUtils.isEmpty(userId) ? "空" : userId));

        builder.addHeader(Fields.PARAM_UID, userId);
        String lan = RateAndLocalManager.GetInstance(CApplication.getInstance()).getCurLocalLanguage();

        if (!lan.isEmpty())
            //TODO 语言配置待定
            builder.addHeader(Fields.PARAM_LANGUAGE, lan);

        /*APPConfig.getInstance().initDevicesParams(BHexApplication.getInstance());
        Map<String, String> deviceParams = APPConfig.getInstance().getDeviceParams();

        Iterator<String> keyIterator = deviceParams.keySet().iterator();

        while (keyIterator.hasNext()) {
            String key = keyIterator.next();
            builder.addParam(key, StringUtils.replaceSpace(deviceParams.get(key)));
        }*/

    }

}
